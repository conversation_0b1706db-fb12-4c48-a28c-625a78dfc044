import os
import numpy as np
import pandas as pd
import cv2
import joblib
import json
import shutil
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, precision_recall_fscore_support
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# Direktori dataset (akan digunakan saat pelatihan)
DATASET_DIR = 'dataset'
MODEL_DIR = 'static/models'
NON_RICE_DATASET_DIR = 'dataset_non_rice'  # Dataset untuk gambar non-padi

# Fungsi untuk ekstraksi fitur dari gambar
def extract_features(image_path, target_size=(224, 224)):
    """
    Ekstraksi fitur dari gambar untuk input model Random Forest
    """
    try:
        # Baca gambar
        img = cv2.imread(image_path)
        if img is None:
            print(f"Error membaca gambar: {image_path}")
            return None

        # Resize gambar ke ukuran tetap
        img = cv2.resize(img, target_size)

        # Konversi ke RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Pendekatan sederhana: hanya gunakan nilai pixel yang dinormalisasi
        # Ini akan memastikan jumlah fitur selalu sama
        features = img.flatten() / 255.0  # Normalisasi

        return features

    except Exception as e:
        print(f"Error saat ekstraksi fitur dari {image_path}: {str(e)}")
        return None

# Fungsi untuk ekstraksi fitur khusus tanaman padi
def extract_rice_features(image_path, target_size=(224, 224)):
    """
    Ekstraksi fitur khusus untuk deteksi tanaman padi
    Menggabungkan fitur warna, tekstur, dan bentuk yang karakteristik tanaman padi
    """
    try:
        # Baca gambar
        img = cv2.imread(image_path)
        if img is None:
            print(f"Error membaca gambar: {image_path}")
            return None

        # Resize gambar ke ukuran tetap
        img = cv2.resize(img, target_size)

        # Konversi ke berbagai color space
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        features = []

        # 1. Fitur warna - Histogram RGB
        for i in range(3):
            hist = cv2.calcHist([img_rgb], [i], None, [32], [0, 256])
            features.extend(hist.flatten())

        # 2. Fitur warna - Histogram HSV (lebih baik untuk deteksi tanaman hijau)
        for i in range(3):
            hist = cv2.calcHist([img_hsv], [i], None, [32], [0, 256])
            features.extend(hist.flatten())

        # 3. Fitur tekstur - Local Binary Pattern sederhana
        # Hitung gradien untuk mendeteksi pola daun
        grad_x = cv2.Sobel(img_gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(img_gray, cv2.CV_64F, 0, 1, ksize=3)

        # Statistik gradien
        features.extend([
            np.mean(grad_x), np.std(grad_x), np.mean(grad_y), np.std(grad_y),
            np.mean(np.abs(grad_x)), np.mean(np.abs(grad_y))
        ])

        # 4. Fitur bentuk - Deteksi tepi
        edges = cv2.Canny(img_gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        features.append(edge_density)

        # 5. Fitur warna hijau (karakteristik tanaman)
        # Deteksi area hijau dalam HSV
        lower_green = np.array([35, 40, 40])
        upper_green = np.array([85, 255, 255])
        green_mask = cv2.inRange(img_hsv, lower_green, upper_green)
        green_ratio = np.sum(green_mask > 0) / (green_mask.shape[0] * green_mask.shape[1])
        features.append(green_ratio)

        # 6. Statistik dasar gambar
        features.extend([
            np.mean(img_rgb), np.std(img_rgb),
            np.mean(img_hsv), np.std(img_hsv),
            np.mean(img_gray), np.std(img_gray)
        ])

        # 7. Fitur pixel yang dinormalisasi (subset dari gambar asli)
        # Ambil sample pixel untuk mengurangi dimensi
        img_small = cv2.resize(img_rgb, (32, 32))
        pixel_features = img_small.flatten() / 255.0
        features.extend(pixel_features)

        return np.array(features)

    except Exception as e:
        print(f"Error saat ekstraksi fitur padi dari {image_path}: {str(e)}")
        return None

# Fungsi untuk memuat dataset
def load_dataset(dataset_dir):
    """
    Memuat dataset gambar dari direktori
    Format direktori yang diharapkan:
    dataset/
        class1/
            image1.jpg
            image2.jpg
            ...
        class2/
            image1.jpg
            ...
        ...
    """
    features = []
    labels = []

    # Periksa apakah direktori dataset ada
    if not os.path.exists(dataset_dir):
        print(f"Direktori dataset {dataset_dir} tidak ditemukan.")
        return None, None

    # Iterasi melalui setiap kelas (subdirektori)
    for class_name in os.listdir(dataset_dir):
        class_dir = os.path.join(dataset_dir, class_name)

        if not os.path.isdir(class_dir):
            continue

        print(f"Memproses kelas: {class_name}")

        # Iterasi melalui setiap gambar dalam kelas
        for image_name in os.listdir(class_dir):
            if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue

            image_path = os.path.join(class_dir, image_name)

            # Ekstrak fitur dari gambar
            feature_vector = extract_features(image_path)

            if feature_vector is not None:
                features.append(feature_vector)
                labels.append(class_name)

    return np.array(features), np.array(labels)

# Fungsi untuk memuat dataset deteksi padi vs non-padi
def load_rice_detection_dataset(rice_dataset_dir, non_rice_dataset_dir):
    """
    Memuat dataset untuk deteksi tanaman padi vs non-padi
    """
    features = []
    labels = []

    # Muat gambar tanaman padi (label = 1)
    if os.path.exists(rice_dataset_dir):
        print("Memuat gambar tanaman padi...")
        for class_name in os.listdir(rice_dataset_dir):
            class_dir = os.path.join(rice_dataset_dir, class_name)

            if not os.path.isdir(class_dir):
                continue

            print(f"Memproses kelas padi: {class_name}")

            # Ambil maksimal 50 gambar per kelas untuk balance
            image_files = [f for f in os.listdir(class_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            image_files = image_files[:50]  # Batasi jumlah

            for image_name in image_files:
                image_path = os.path.join(class_dir, image_name)
                feature_vector = extract_rice_features(image_path)

                if feature_vector is not None:
                    features.append(feature_vector)
                    labels.append(1)  # Label 1 untuk tanaman padi

    # Muat gambar non-padi (label = 0)
    if os.path.exists(non_rice_dataset_dir):
        print("Memuat gambar non-padi...")
        for item in os.listdir(non_rice_dataset_dir):
            item_path = os.path.join(non_rice_dataset_dir, item)

            if os.path.isfile(item_path) and item.lower().endswith(('.png', '.jpg', '.jpeg')):
                # File langsung di direktori non_rice
                feature_vector = extract_rice_features(item_path)
                if feature_vector is not None:
                    features.append(feature_vector)
                    labels.append(0)  # Label 0 untuk non-padi

            elif os.path.isdir(item_path):
                # Subdirektori dalam non_rice
                print(f"Memproses kelas non-padi: {item}")
                image_files = [f for f in os.listdir(item_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                image_files = image_files[:50]  # Batasi jumlah

                for image_name in image_files:
                    image_path = os.path.join(item_path, image_name)
                    feature_vector = extract_rice_features(image_path)

                    if feature_vector is not None:
                        features.append(feature_vector)
                        labels.append(0)  # Label 0 untuk non-padi

    if len(features) == 0:
        print("Tidak ada data yang berhasil dimuat!")
        return None, None

    print(f"Dataset deteksi padi dimuat: {len(features)} sampel")
    print(f"Tanaman padi: {sum(labels)} sampel")
    print(f"Non-padi: {len(labels) - sum(labels)} sampel")

    return np.array(features), np.array(labels)

# Fungsi untuk melatih model Random Forest
def train_model(dataset_dir=DATASET_DIR, model_dir=MODEL_DIR):
    """
    Melatih model Random Forest untuk klasifikasi penyakit tanaman padi
    """
    try:
        # Buat direktori model jika belum ada
        os.makedirs(model_dir, exist_ok=True)

        # Muat dataset
        print("Memuat dataset...")
        X, y = load_dataset(dataset_dir)

        if X is None or len(X) == 0:
            return {
                "success": False,
                "error": "Dataset tidak ditemukan atau kosong"
            }

        print(f"Dataset dimuat: {X.shape[0]} sampel dengan {X.shape[1]} fitur")

        # Split dataset
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Inisialisasi dan latih model Random Forest
        print("Melatih model Random Forest...")
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )

        model.fit(X_train, y_train)

        # Evaluasi model
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred) * 100

        # Dapatkan laporan klasifikasi lengkap
        report = classification_report(y_test, y_pred, output_dict=True)
        report_str = classification_report(y_test, y_pred)

        print(f"Akurasi model: {accuracy:.2f}%")
        print("\nLaporan Klasifikasi:")
        print(report_str)

        # Hitung precision, recall, dan f1-score untuk setiap kelas
        precision, recall, f1, support = precision_recall_fscore_support(y_test, y_pred, average=None)

        # Buat dictionary untuk metrik per kelas
        class_metrics = {}
        for i, class_name in enumerate(model.classes_):
            class_metrics[class_name] = {
                "precision": precision[i],
                "recall": recall[i],
                "f1": f1[i],
                "support": int(support[i])
            }

        # Simpan confusion matrix sebagai gambar
        plt.figure(figsize=(10, 8))
        cm = confusion_matrix(y_test, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=model.classes_, yticklabels=model.classes_)
        plt.xlabel('Prediksi')
        plt.ylabel('Aktual')
        plt.title('Confusion Matrix')
        cm_path = os.path.join(model_dir, 'confusion_matrix.png')
        plt.savefig(cm_path)
        plt.close()

        # Buat direktori untuk contoh prediksi
        examples_dir = os.path.join(model_dir, 'examples')
        if os.path.exists(examples_dir):
            shutil.rmtree(examples_dir)
        os.makedirs(examples_dir, exist_ok=True)

        # Simpan beberapa contoh prediksi (benar dan salah)
        examples = []
        correct_indices = []
        incorrect_indices = []

        for i, (true, pred) in enumerate(zip(y_test, y_pred)):
            if true == pred:
                correct_indices.append(i)
            else:
                incorrect_indices.append(i)

        # Ambil maksimal 3 contoh benar dan 3 contoh salah
        max_examples = 3
        selected_correct = correct_indices[:min(max_examples, len(correct_indices))]
        selected_incorrect = incorrect_indices[:min(max_examples, len(incorrect_indices))]

        # Gabungkan indeks yang dipilih
        selected_indices = selected_correct + selected_incorrect

        # Simpan contoh-contoh
        for idx, i in enumerate(selected_indices):
            # Dapatkan probabilitas untuk contoh ini
            probs = model.predict_proba([X_test[i]])[0]
            confidence = float(max(probs) * 100)

            # Buat gambar dari fitur untuk contoh ini
            img_features = X_test[i].reshape((224, 224, 3)) * 255  # Ubah kembali ke skala 0-255
            img_features = img_features.astype(np.uint8)

            # Simpan gambar contoh
            example_img_path = os.path.join(examples_dir, f"example_{idx}.png")
            cv2.imwrite(example_img_path, cv2.cvtColor(img_features, cv2.COLOR_RGB2BGR))

            # Buat contoh
            example = {
                "true_label": y_test[i],
                "predicted_label": y_pred[i],
                "confidence": confidence,
                "image": f"example_{idx}.png"
            }
            examples.append(example)

        # Simpan model
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        model_path = os.path.join(model_dir, 'random_forest_model.pkl')
        joblib.dump(model, model_path)

        # Simpan juga model dengan timestamp untuk history
        history_model_path = os.path.join(model_dir, f'random_forest_model_{timestamp}.pkl')
        joblib.dump(model, history_model_path)

        print(f"Model disimpan di: {model_path}")

        # Simpan informasi model yang lebih lengkap
        model_info = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "accuracy": accuracy,
            "n_samples": X.shape[0],
            "test_samples": len(y_test),
            "n_features": X.shape[1],
            "classes": list(model.classes_),
            "class_metrics": class_metrics,
            "classification_report": report_str,
            "examples": examples,
            "model_params": {
                "n_estimators": model.n_estimators,
                "max_depth": model.max_depth if model.max_depth is not None else "None",
                "min_samples_split": model.min_samples_split,
                "min_samples_leaf": model.min_samples_leaf
            }
        }

        # Simpan informasi model sebagai JSON
        model_info_json_path = os.path.join(model_dir, 'model_info.json')
        with open(model_info_json_path, 'w') as f:
            json.dump(model_info, f, indent=4, default=str)

        # Simpan juga sebagai text untuk kompatibilitas
        model_info_path = os.path.join(model_dir, 'model_info.txt')
        with open(model_info_path, 'w') as f:
            for key, value in model_info.items():
                if key not in ["class_metrics", "examples", "model_params", "classification_report"]:
                    f.write(f"{key}: {value}\n")

        return {
            "success": True,
            "accuracy": accuracy,
            "model_path": model_path,
            "classes": list(model.classes_)
        }

    except Exception as e:
        print(f"Error saat melatih model: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Fungsi untuk memprediksi gambar baru
def predict_image(image_path, model_path=os.path.join(MODEL_DIR, 'random_forest_model.pkl')):
    """
    Memprediksi penyakit dari gambar tanaman padi
    """
    try:
        # Periksa apakah model ada
        if not os.path.exists(model_path):
            return {
                "success": False,
                "error": "Model tidak ditemukan"
            }

        # Muat model
        model = joblib.load(model_path)

        # Ekstrak fitur dari gambar
        features = extract_features(image_path)

        if features is None:
            return {
                "success": False,
                "error": "Gagal mengekstrak fitur dari gambar"
            }

        # Prediksi
        prediction = model.predict([features])[0]
        probabilities = model.predict_proba([features])[0]

        # Dapatkan nama kelas
        class_names = model.classes_

        # Buat hasil
        result = {
            "success": True,
            "prediction": prediction,
            "confidence": float(max(probabilities) * 100),
            "probabilities": {class_names[i]: float(prob * 100) for i, prob in enumerate(probabilities)}
        }

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Fungsi untuk melatih model deteksi tanaman padi
def train_rice_detection_model(rice_dataset_dir=DATASET_DIR, non_rice_dataset_dir=NON_RICE_DATASET_DIR, model_dir=MODEL_DIR):
    """
    Melatih model untuk deteksi tanaman padi vs non-padi
    """
    try:
        # Buat direktori model jika belum ada
        os.makedirs(model_dir, exist_ok=True)

        # Muat dataset
        print("Memuat dataset deteksi padi...")
        X, y = load_rice_detection_dataset(rice_dataset_dir, non_rice_dataset_dir)

        if X is None or len(X) == 0:
            return {
                "success": False,
                "error": "Dataset deteksi padi tidak ditemukan atau kosong"
            }

        print(f"Dataset deteksi padi dimuat: {X.shape[0]} sampel dengan {X.shape[1]} fitur")

        # Split dataset
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

        # Inisialisasi dan latih model Random Forest untuk deteksi padi
        print("Melatih model deteksi tanaman padi...")
        rice_detector = RandomForestClassifier(
            n_estimators=100,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )

        rice_detector.fit(X_train, y_train)

        # Evaluasi model
        y_pred = rice_detector.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred) * 100

        print(f"Akurasi model deteksi padi: {accuracy:.2f}%")

        # Simpan model deteksi padi
        rice_detector_path = os.path.join(model_dir, 'rice_detector_model.pkl')
        joblib.dump(rice_detector, rice_detector_path)

        print(f"Model deteksi padi disimpan di: {rice_detector_path}")

        # Simpan informasi model deteksi
        detector_info = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "accuracy": accuracy,
            "n_samples": X.shape[0],
            "test_samples": len(y_test),
            "n_features": X.shape[1],
            "rice_samples": int(sum(y)),
            "non_rice_samples": int(len(y) - sum(y))
        }

        detector_info_path = os.path.join(model_dir, 'rice_detector_info.json')
        with open(detector_info_path, 'w') as f:
            json.dump(detector_info, f, indent=4)

        return {
            "success": True,
            "accuracy": accuracy,
            "model_path": rice_detector_path,
            "rice_samples": int(sum(y)),
            "non_rice_samples": int(len(y) - sum(y))
        }

    except Exception as e:
        print(f"Error saat melatih model deteksi padi: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Fungsi untuk memprediksi apakah gambar adalah tanaman padi
def is_rice_plant(image_path, model_path=os.path.join(MODEL_DIR, 'rice_detector_model.pkl')):
    """
    Memprediksi apakah gambar adalah tanaman padi atau bukan
    Returns: True jika tanaman padi, False jika bukan
    """
    try:
        # Periksa apakah model deteksi padi ada
        if not os.path.exists(model_path):
            print("Model deteksi padi tidak ditemukan, menggunakan deteksi sederhana...")
            # Fallback: deteksi sederhana berdasarkan warna hijau
            return simple_rice_detection(image_path)

        # Muat model deteksi padi
        rice_detector = joblib.load(model_path)

        # Ekstrak fitur khusus tanaman padi
        features = extract_rice_features(image_path)

        if features is None:
            return False

        # Prediksi
        prediction = rice_detector.predict([features])[0]
        probability = rice_detector.predict_proba([features])[0]

        # Ambil probabilitas untuk kelas padi (label 1)
        rice_probability = probability[1] if len(probability) > 1 else probability[0]

        print(f"Probabilitas tanaman padi: {rice_probability:.2f}")

        # Return True jika diprediksi sebagai tanaman padi dengan confidence > 0.5
        return prediction == 1 and rice_probability > 0.5

    except Exception as e:
        print(f"Error saat deteksi tanaman padi: {str(e)}")
        # Fallback ke deteksi sederhana
        return simple_rice_detection(image_path)

# Fungsi deteksi sederhana tanaman padi berdasarkan warna hijau
def simple_rice_detection(image_path):
    """
    Deteksi sederhana tanaman padi berdasarkan proporsi warna hijau
    """
    try:
        img = cv2.imread(image_path)
        if img is None:
            return False

        # Resize untuk konsistensi
        img = cv2.resize(img, (224, 224))
        img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # Deteksi area hijau (karakteristik tanaman)
        lower_green = np.array([35, 40, 40])
        upper_green = np.array([85, 255, 255])
        green_mask = cv2.inRange(img_hsv, lower_green, upper_green)

        # Hitung proporsi area hijau
        green_ratio = np.sum(green_mask > 0) / (green_mask.shape[0] * green_mask.shape[1])

        print(f"Proporsi area hijau: {green_ratio:.2f}")

        # Jika proporsi hijau > 30%, anggap sebagai tanaman
        return green_ratio > 0.3

    except Exception as e:
        print(f"Error saat deteksi sederhana: {str(e)}")
        return False

# Jika file ini dijalankan langsung
if __name__ == "__main__":
    # Latih model jika dataset tersedia
    if os.path.exists(DATASET_DIR):
        result = train_model()
        print(result)
    else:
        print(f"Dataset tidak ditemukan di {DATASET_DIR}. Buat direktori dataset dengan struktur yang benar terlebih dahulu.")
