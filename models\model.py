import os
import numpy as np
import pandas as pd
import cv2
import joblib
import json
import shutil
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, precision_recall_fscore_support
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# Direktori dataset (akan digunakan saat pelatihan)
DATASET_DIR = 'dataset'
MODEL_DIR = 'static/models'

# Fungsi untuk ekstraksi fitur dari gambar
def extract_features(image_path, target_size=(224, 224)):
    """
    Ekstraksi fitur dari gambar untuk input model Random Forest
    """
    try:
        # Baca gambar
        img = cv2.imread(image_path)
        if img is None:
            print(f"Error membaca gambar: {image_path}")
            return None

        # Resize gambar ke ukuran tetap
        img = cv2.resize(img, target_size)

        # Konversi ke RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Pendekatan sederhana: hanya gunakan nilai pixel yang dinormalisasi
        # Ini akan memastikan jumlah fitur selalu sama
        features = img.flatten() / 255.0  # Normalisasi

        return features

    except Exception as e:
        print(f"Error saat ekstraksi fitur dari {image_path}: {str(e)}")
        return None

# Fungsi untuk memuat dataset
def load_dataset(dataset_dir):
    """
    Memuat dataset gambar dari direktori
    Format direktori yang diharapkan:
    dataset/
        class1/
            image1.jpg
            image2.jpg
            ...
        class2/
            image1.jpg
            ...
        ...
    """
    features = []
    labels = []

    # Periksa apakah direktori dataset ada
    if not os.path.exists(dataset_dir):
        print(f"Direktori dataset {dataset_dir} tidak ditemukan.")
        return None, None

    # Iterasi melalui setiap kelas (subdirektori)
    for class_name in os.listdir(dataset_dir):
        class_dir = os.path.join(dataset_dir, class_name)

        if not os.path.isdir(class_dir):
            continue

        print(f"Memproses kelas: {class_name}")

        # Iterasi melalui setiap gambar dalam kelas
        for image_name in os.listdir(class_dir):
            if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue

            image_path = os.path.join(class_dir, image_name)

            # Ekstrak fitur dari gambar
            feature_vector = extract_features(image_path)

            if feature_vector is not None:
                features.append(feature_vector)
                labels.append(class_name)

    return np.array(features), np.array(labels)

# Fungsi untuk melatih model Random Forest
def train_model(dataset_dir=DATASET_DIR, model_dir=MODEL_DIR):
    """
    Melatih model Random Forest untuk klasifikasi penyakit tanaman padi
    """
    try:
        # Buat direktori model jika belum ada
        os.makedirs(model_dir, exist_ok=True)

        # Muat dataset
        print("Memuat dataset...")
        X, y = load_dataset(dataset_dir)

        if X is None or len(X) == 0:
            return {
                "success": False,
                "error": "Dataset tidak ditemukan atau kosong"
            }

        print(f"Dataset dimuat: {X.shape[0]} sampel dengan {X.shape[1]} fitur")

        # Split dataset
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Inisialisasi dan latih model Random Forest
        print("Melatih model Random Forest...")
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )

        model.fit(X_train, y_train)

        # Evaluasi model
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred) * 100

        # Dapatkan laporan klasifikasi lengkap
        report = classification_report(y_test, y_pred, output_dict=True)
        report_str = classification_report(y_test, y_pred)

        print(f"Akurasi model: {accuracy:.2f}%")
        print("\nLaporan Klasifikasi:")
        print(report_str)

        # Hitung precision, recall, dan f1-score untuk setiap kelas
        precision, recall, f1, support = precision_recall_fscore_support(y_test, y_pred, average=None)

        # Buat dictionary untuk metrik per kelas
        class_metrics = {}
        for i, class_name in enumerate(model.classes_):
            class_metrics[class_name] = {
                "precision": precision[i],
                "recall": recall[i],
                "f1": f1[i],
                "support": int(support[i])
            }

        # Simpan confusion matrix sebagai gambar
        plt.figure(figsize=(10, 8))
        cm = confusion_matrix(y_test, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=model.classes_, yticklabels=model.classes_)
        plt.xlabel('Prediksi')
        plt.ylabel('Aktual')
        plt.title('Confusion Matrix')
        cm_path = os.path.join(model_dir, 'confusion_matrix.png')
        plt.savefig(cm_path)
        plt.close()

        # Buat direktori untuk contoh prediksi
        examples_dir = os.path.join(model_dir, 'examples')
        if os.path.exists(examples_dir):
            shutil.rmtree(examples_dir)
        os.makedirs(examples_dir, exist_ok=True)

        # Simpan beberapa contoh prediksi (benar dan salah)
        examples = []
        correct_indices = []
        incorrect_indices = []

        for i, (true, pred) in enumerate(zip(y_test, y_pred)):
            if true == pred:
                correct_indices.append(i)
            else:
                incorrect_indices.append(i)

        # Ambil maksimal 3 contoh benar dan 3 contoh salah
        max_examples = 3
        selected_correct = correct_indices[:min(max_examples, len(correct_indices))]
        selected_incorrect = incorrect_indices[:min(max_examples, len(incorrect_indices))]

        # Gabungkan indeks yang dipilih
        selected_indices = selected_correct + selected_incorrect

        # Simpan contoh-contoh
        for idx, i in enumerate(selected_indices):
            # Dapatkan probabilitas untuk contoh ini
            probs = model.predict_proba([X_test[i]])[0]
            confidence = float(max(probs) * 100)

            # Buat gambar dari fitur untuk contoh ini
            img_features = X_test[i].reshape((224, 224, 3)) * 255  # Ubah kembali ke skala 0-255
            img_features = img_features.astype(np.uint8)

            # Simpan gambar contoh
            example_img_path = os.path.join(examples_dir, f"example_{idx}.png")
            cv2.imwrite(example_img_path, cv2.cvtColor(img_features, cv2.COLOR_RGB2BGR))

            # Buat contoh
            example = {
                "true_label": y_test[i],
                "predicted_label": y_pred[i],
                "confidence": confidence,
                "image": f"example_{idx}.png"
            }
            examples.append(example)

        # Simpan model
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        model_path = os.path.join(model_dir, 'random_forest_model.pkl')
        joblib.dump(model, model_path)

        # Simpan juga model dengan timestamp untuk history
        history_model_path = os.path.join(model_dir, f'random_forest_model_{timestamp}.pkl')
        joblib.dump(model, history_model_path)

        print(f"Model disimpan di: {model_path}")

        # Simpan informasi model yang lebih lengkap
        model_info = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "accuracy": accuracy,
            "n_samples": X.shape[0],
            "test_samples": len(y_test),
            "n_features": X.shape[1],
            "classes": list(model.classes_),
            "class_metrics": class_metrics,
            "classification_report": report_str,
            "examples": examples,
            "model_params": {
                "n_estimators": model.n_estimators,
                "max_depth": model.max_depth if model.max_depth is not None else "None",
                "min_samples_split": model.min_samples_split,
                "min_samples_leaf": model.min_samples_leaf
            }
        }

        # Simpan informasi model sebagai JSON
        model_info_json_path = os.path.join(model_dir, 'model_info.json')
        with open(model_info_json_path, 'w') as f:
            json.dump(model_info, f, indent=4, default=str)

        # Simpan juga sebagai text untuk kompatibilitas
        model_info_path = os.path.join(model_dir, 'model_info.txt')
        with open(model_info_path, 'w') as f:
            for key, value in model_info.items():
                if key not in ["class_metrics", "examples", "model_params", "classification_report"]:
                    f.write(f"{key}: {value}\n")

        return {
            "success": True,
            "accuracy": accuracy,
            "model_path": model_path,
            "classes": list(model.classes_)
        }

    except Exception as e:
        print(f"Error saat melatih model: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Fungsi untuk memprediksi gambar baru
def predict_image(image_path, model_path=os.path.join(MODEL_DIR, 'random_forest_model.pkl')):
    """
    Memprediksi penyakit dari gambar tanaman padi
    """
    try:
        # Periksa apakah model ada
        if not os.path.exists(model_path):
            return {
                "success": False,
                "error": "Model tidak ditemukan"
            }

        # Muat model
        model = joblib.load(model_path)

        # Ekstrak fitur dari gambar
        features = extract_features(image_path)

        if features is None:
            return {
                "success": False,
                "error": "Gagal mengekstrak fitur dari gambar"
            }

        # Prediksi
        prediction = model.predict([features])[0]
        probabilities = model.predict_proba([features])[0]

        # Dapatkan nama kelas
        class_names = model.classes_

        # Buat hasil
        result = {
            "success": True,
            "prediction": prediction,
            "confidence": float(max(probabilities) * 100),
            "probabilities": {class_names[i]: float(prob * 100) for i, prob in enumerate(probabilities)}
        }

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Jika file ini dijalankan langsung
if __name__ == "__main__":
    # Latih model jika dataset tersedia
    if os.path.exists(DATASET_DIR):
        result = train_model()
        print(result)
    else:
        print(f"Dataset tidak ditemukan di {DATASET_DIR}. Buat direktori dataset dengan struktur yang benar terlebih dahulu.")
