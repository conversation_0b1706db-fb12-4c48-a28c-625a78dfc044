{% extends "base.html" %}

{% block title %}Dashboard - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.stat-card {
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    background-color: white;
    margin-bottom: 20px;
}
.stat-card i {
    font-size: 40px;
    color: #28a745;
    margin-bottom: 15px;
}
.stat-number {
    font-size: 30px;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 16px;
    color: #6c757d;
}
{% endblock %}

{% block content %}
<h2 class="mb-4">Dashboard</h2>

<div class="row">
    <div class="col-md-6">
        <div class="card stat-card">
            <i class="fas fa-users"></i>
            <div class="stat-number">{{ total_users }}</div>
            <div class="stat-label">Total Pengguna</div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card stat-card">
            <i class="fas fa-microscope"></i>
            <div class="stat-number">{{ total_classifications }}</div>
            <div class="stat-label">Total Klasifikasi</div>
        </div>
    </div>
</div>

{% if current_user.role == 'admin' %}
<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-history me-2"></i> Klasifikasi Terbaru (Admin Only)
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Pengguna</th>
                        <th>Gambar</th>
                        <th>Prediksi</th>
                        <th>Kepercayaan</th>
                        <th>Tanggal</th>
                    </tr>
                </thead>
                <tbody>
                    {% for classification in recent_classifications %}
                    <tr>
                        <td>{{ classification.id }}</td>
                        <td>{{ classification.username }}</td>
                        <td>
                            <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                 alt="Gambar Tanaman" class="img-thumbnail" style="width: 50px;">
                        </td>
                        <td>{{ classification.prediction }}</td>
                        <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                        <td>{{ classification.created_at }}</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">Belum ada data klasifikasi</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-info-circle me-2"></i> Informasi
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-0">
            <i class="fas fa-lightbulb me-2"></i> Untuk melihat riwayat klasifikasi Anda, silakan kunjungi halaman <a href="{{ url_for('profile') }}">Profil</a>.
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
