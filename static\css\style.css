/* <PERSON><PERSON> umum */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* <PERSON>a sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 250px;
    background-color: #28a745;
    padding-top: 20px;
    color: white;
}
.sidebar-header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
.sidebar-header img {
    width: 60px;
    margin-bottom: 10px;
}
.sidebar-menu {
    padding: 20px 0;
}
.sidebar-menu a {
    display: block;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s;
}
.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
}
.sidebar-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}
.main-content {
    margin-left: 250px;
    padding: 20px;
}

/* Gaya kartu */
.card {
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Gaya login */
.login-container {
    max-width: 450px;
    margin: 100px auto;
    padding: 30px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}
.login-header {
    text-align: center;
    margin-bottom: 30px;
}
.login-header img {
    width: 80px;
    margin-bottom: 15px;
}
.login-header h2 {
    color: #28a745;
    font-weight: 600;
}
.form-control {
    padding: 12px;
    border-radius: 5px;
    margin-bottom: 20px;
}
.btn-login {
    background-color: #28a745;
    border: none;
    padding: 12px;
    font-weight: 600;
    width: 100%;
    border-radius: 5px;
}
.btn-login:hover {
    background-color: #218838;
}
.login-footer {
    text-align: center;
    margin-top: 20px;
    color: #6c757d;
}

/* Gaya profil */
.profile-header {
    text-align: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 20px;
}
.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin-bottom: 20px;
    object-fit: cover;
}
.profile-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}
.profile-role {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 15px;
}
.profile-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}
.stat-item {
    text-align: center;
}
.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #28a745;
}
.stat-label {
    font-size: 14px;
    color: #6c757d;
}

/* Gaya upload area */
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}
.upload-area:hover {
    background-color: #e9ecef;
}
.upload-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}

/* Gaya user info */
.user-info {
    text-align: center;
    margin-top: auto;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}
.user-info img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 10px;
}
