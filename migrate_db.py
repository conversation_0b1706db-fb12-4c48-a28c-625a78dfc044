import sqlite3
import os

def migrate_database():
    print("Memulai migrasi database...")
    
    # Pastikan direktori database ada
    os.makedirs('database', exist_ok=True)
    
    # Koneksi ke database
    conn = sqlite3.connect('database/padiku.db')
    cursor = conn.cursor()
    
    # Cek apakah kolom full_name dan address sudah ada di tabel users
    cursor.execute("PRAGMA table_info(users)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]
    
    # Tambahkan kolom full_name jika belum ada
    if 'full_name' not in column_names:
        print("Menambahkan kolom full_name ke tabel users...")
        cursor.execute("ALTER TABLE users ADD COLUMN full_name TEXT")
    
    # Tambahkan kolom address jika belum ada
    if 'address' not in column_names:
        print("Menambahkan kolom address ke tabel users...")
        cursor.execute("ALTER TABLE users ADD COLUMN address TEXT")
    
    # Commit perubahan dan tutup koneksi
    conn.commit()
    conn.close()
    
    print("Migrasi database selesai!")

if __name__ == "__main__":
    migrate_database()
