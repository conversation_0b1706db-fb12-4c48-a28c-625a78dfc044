DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS classifications;
DROP TABLE IF EXISTS diseases;

CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    full_name TEXT,
    address TEXT,
    role TEXT NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE diseases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    symptoms TEXT,
    treatment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE classifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    image_path TEXT NOT NULL,
    prediction TEXT NOT NULL,
    confidence REAL NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role)
VALUES ('admin', '<EMAIL>', 'pbkdf2:sha256:150000$KKgd9zci$fc2a8c967b1a41d5f591af95a7a9b58a9e0a7c28f0d517b9a27d4f15a6d1a342', 'admin');

-- Insert default user (password: user123)
INSERT INTO users (username, email, password, role)
VALUES ('user', '<EMAIL>', 'pbkdf2:sha256:150000$lc7yzntS$c57a8edd9c7f9b8f3b1a544c8f48b4a293995aeba0e5471b0bf9f0688b34b6e6', 'user');

-- Insert penyakit tanaman padi
INSERT INTO diseases (name, description, symptoms, treatment)
VALUES
('Sehat',
'Tanaman padi yang sehat menunjukkan pertumbuhan normal tanpa tanda-tanda penyakit.',
'Daun hijau segar, batang tegak, pertumbuhan normal.',
'Perawatan rutin seperti pemupukan seimbang dan pengairan yang cukup.');

INSERT INTO diseases (name, description, symptoms, treatment)
VALUES
('Blast',
'Penyakit blast disebabkan oleh jamur Pyricularia oryzae. Ini adalah salah satu penyakit padi yang paling merusak di seluruh dunia.',
'Bercak berbentuk belah ketupat pada daun, berwarna coklat dengan pusat abu-abu. Pada kondisi parah, seluruh daun bisa mati.',
'Gunakan varietas tahan blast, aplikasi fungisida berbahan aktif difenokonazol, azoksistrobin, atau trisiklazol. Hindari pemupukan nitrogen berlebihan.');

INSERT INTO diseases (name, description, symptoms, treatment)
VALUES
('Brown Spot',
'Penyakit bercak coklat disebabkan oleh jamur Cochliobolus miyabeanus. Sering terjadi pada tanah dengan kesuburan rendah.',
'Bercak coklat oval pada daun, kadang dengan tepi kuning. Bercak bisa bergabung membentuk area nekrotik yang lebih besar.',
'Perbaiki kesuburan tanah, terutama kekurangan kalium. Aplikasi fungisida berbahan aktif mancozeb atau propineb. Gunakan benih sehat.');

INSERT INTO diseases (name, description, symptoms, treatment)
VALUES
('Bacterial Leaf Blight',
'Hawar daun bakteri disebabkan oleh bakteri Xanthomonas oryzae pv. oryzae. Penyakit ini dapat menyebabkan kehilangan hasil hingga 70%.',
'Bercak kuning hingga putih pada tepi daun yang kemudian meluas ke bagian tengah. Daun yang terinfeksi akhirnya mengering dan mati.',
'Gunakan varietas tahan, hindari pemupukan nitrogen berlebihan, aplikasi bakterisida berbahan tembaga, dan drainase yang baik.');

INSERT INTO diseases (name, description, symptoms, treatment)
VALUES
('Tungro',
'Penyakit tungro disebabkan oleh kombinasi dua virus yang ditularkan oleh wereng hijau. Ini adalah penyakit virus paling merusak pada padi di Asia Tenggara.',
'Daun menguning hingga oranye, pertumbuhan kerdil, jumlah anakan berkurang, dan malai kecil dengan banyak gabah hampa.',
'Gunakan varietas tahan, tanam serempak, kendalikan vektor wereng hijau dengan insektisida, dan bersihkan gulma inang.');
