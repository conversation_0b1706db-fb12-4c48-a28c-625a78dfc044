import json
import os

# Test baca file JSON langsung
print("=== Test File JSON ===")
json_path = 'static/models/model_info.json'
if os.path.exists(json_path):
    with open(json_path, 'r') as f:
        data = json.load(f)
    print('✅ File JSON berhasil dibaca')
    print(f'Timestamp: {data.get("timestamp")}')
    print(f'Accuracy: {data.get("accuracy")}')
    print(f'Classes: {data.get("classes")}')
else:
    print('❌ File JSON tidak ditemukan')

# Test file model
print("\n=== Test File Model ===")
model_path = 'static/models/random_forest_model.pkl'
if os.path.exists(model_path):
    print('✅ File model ditemukan')
else:
    print('❌ File model tidak ditemukan')
