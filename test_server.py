import requests
import json

print("=== Test Server Response ===")

try:
    # Test endpoint utama
    response = requests.get('http://localhost:5000/')
    print(f"Homepage status: {response.status_code}")
    
    # Test endpoint API
    response = requests.get('http://localhost:5000/api/model_info')
    print(f"API status: {response.status_code}")
    print(f"API response: {response.text}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("JSON parsed successfully:")
            print(json.dumps(data, indent=2))
        except:
            print("Failed to parse JSON")
    
except Exception as e:
    print(f"Error: {e}")
