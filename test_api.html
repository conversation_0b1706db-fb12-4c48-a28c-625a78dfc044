<!DOCTYPE html>
<html>
<head>
    <title>Test API Model Info</title>
</head>
<body>
    <h1>Test API Model Info</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
    function testAPI() {
        console.log('Testing API...');
        document.getElementById('result').innerHTML = 'Loading...';
        
        fetch('/api/model_info')
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                document.getElementById('result').innerHTML = '<pre>' + text + '</pre>';
                
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed JSON:', data);
                } catch (e) {
                    console.error('JSON parse error:', e);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            });
    }
    </script>
</body>
</html>
