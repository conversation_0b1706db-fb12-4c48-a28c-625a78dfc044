#!/usr/bin/env python3
"""
Script untuk membuat dataset contoh non-padi untuk testing
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def create_sample_non_rice_images():
    """
    Membuat beberapa gambar contoh non-padi untuk testing
    """
    # Buat direktori dataset non-padi
    base_dir = "dataset_non_rice"
    categories = ["buildings", "animals", "vehicles", "other_plants", "objects"]
    
    for category in categories:
        category_dir = os.path.join(base_dir, category)
        os.makedirs(category_dir, exist_ok=True)
    
    # Ukuran gambar standar
    img_size = (224, 224)
    
    # 1. Gambar bangunan (warna abu-abu/coklat)
    for i in range(5):
        img = np.random.randint(100, 150, (*img_size, 3), dtype=np.uint8)  # Abu-abu
        # Tambahkan beberapa garis untuk simulasi bangunan
        cv2.rectangle(img, (50, 50), (174, 174), (80, 80, 80), -1)
        cv2.rectangle(img, (60, 60), (164, 164), (120, 120, 120), 2)
        cv2.imwrite(os.path.join(base_dir, "buildings", f"building_{i+1}.jpg"), img)
    
    # 2. Gambar hewan (warna coklat/hitam)
    for i in range(5):
        img = np.random.randint(80, 120, (*img_size, 3), dtype=np.uint8)  # Coklat gelap
        # Tambahkan bentuk oval untuk simulasi hewan
        cv2.ellipse(img, (112, 112), (80, 50), 0, 0, 360, (60, 40, 20), -1)
        cv2.imwrite(os.path.join(base_dir, "animals", f"animal_{i+1}.jpg"), img)
    
    # 3. Gambar kendaraan (warna metalik)
    for i in range(5):
        img = np.random.randint(150, 200, (*img_size, 3), dtype=np.uint8)  # Metalik
        # Tambahkan bentuk persegi panjang untuk simulasi kendaraan
        cv2.rectangle(img, (30, 80), (194, 144), (100, 100, 150), -1)
        cv2.circle(img, (60, 144), 15, (50, 50, 50), -1)  # Roda
        cv2.circle(img, (164, 144), 15, (50, 50, 50), -1)  # Roda
        cv2.imwrite(os.path.join(base_dir, "vehicles", f"vehicle_{i+1}.jpg"), img)
    
    # 4. Tanaman lain (warna hijau tapi pola berbeda dari padi)
    for i in range(5):
        img = np.random.randint(20, 60, (*img_size, 3), dtype=np.uint8)  # Hijau gelap
        img[:, :, 1] = np.random.randint(100, 180, img_size)  # Channel hijau lebih terang
        # Tambahkan pola daun yang berbeda dari padi
        cv2.circle(img, (112, 112), 60, (30, 150, 30), -1)
        cv2.circle(img, (80, 80), 30, (40, 180, 40), -1)
        cv2.circle(img, (144, 144), 30, (40, 180, 40), -1)
        cv2.imwrite(os.path.join(base_dir, "other_plants", f"other_plant_{i+1}.jpg"), img)
    
    # 5. Objek lain (warna random)
    for i in range(5):
        img = np.random.randint(0, 255, (*img_size, 3), dtype=np.uint8)  # Random
        # Tambahkan bentuk geometris random
        cv2.rectangle(img, (50, 50), (174, 174), 
                     (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)), -1)
        cv2.imwrite(os.path.join(base_dir, "objects", f"object_{i+1}.jpg"), img)
    
    print(f"Dataset non-padi contoh berhasil dibuat di direktori: {base_dir}")
    print("Struktur direktori:")
    for category in categories:
        category_dir = os.path.join(base_dir, category)
        file_count = len([f for f in os.listdir(category_dir) if f.endswith('.jpg')])
        print(f"  {category}/: {file_count} gambar")

def create_readme():
    """
    Membuat file README untuk dataset non-padi
    """
    readme_content = """# Dataset Non-Padi

Dataset ini berisi gambar-gambar yang bukan tanaman padi untuk melatih model deteksi tanaman padi.

## Struktur Dataset

- **buildings/**: Gambar bangunan dan struktur buatan manusia
- **animals/**: Gambar hewan
- **vehicles/**: Gambar kendaraan
- **other_plants/**: Gambar tanaman selain padi
- **objects/**: Gambar objek lainnya

## Penggunaan

Dataset ini digunakan untuk melatih model binary classifier yang dapat membedakan antara:
- Tanaman padi (label: 1)
- Bukan tanaman padi (label: 0)

## Catatan

Gambar-gambar dalam dataset ini adalah gambar sintetis yang dibuat untuk keperluan testing.
Untuk penggunaan produksi, disarankan menggunakan gambar real yang lebih beragam.
"""
    
    with open("dataset_non_rice/README.md", "w") as f:
        f.write(readme_content)
    
    print("File README.md berhasil dibuat")

if __name__ == "__main__":
    print("Membuat dataset contoh non-padi...")
    create_sample_non_rice_images()
    create_readme()
    print("Selesai!")
