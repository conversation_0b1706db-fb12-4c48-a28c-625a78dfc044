{% extends "base.html" %}

{% block title %}Kelola Penyakit - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.disease-badge {
    background-color: #28a745;
}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Kelola Penyakit</h2>
    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDiseaseModal">
        <i class="fas fa-plus me-2"></i> Tambah Penyakit
    </button>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-virus me-2"></i> Daftar Penyakit
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama</th>
                        <th><PERSON><PERSON><PERSON><PERSON></th>
                        <th>Tanggal Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    {% for disease in diseases %}
                    <tr>
                        <td>{{ disease.id }}</td>
                        <td>
                            <span class="badge disease-badge">{{ disease.name }}</span>
                        </td>
                        <td>{{ disease.description|truncate(100) }}</td>
                        <td>{{ disease.created_at }}</td>
                        <td>
                            <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDiseaseModal{{ disease.id }}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDiseaseModal{{ disease.id }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDiseaseModal{{ disease.id }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    
                    <!-- View Disease Modal -->
                    <div class="modal fade" id="viewDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="viewDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="viewDiseaseModalLabel{{ disease.id }}">Detail Penyakit: {{ disease.name }}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <h5>Deskripsi</h5>
                                    <p>{{ disease.description }}</p>
                                    
                                    <h5>Gejala</h5>
                                    <p>{{ disease.symptoms }}</p>
                                    
                                    <h5>Penanganan</h5>
                                    <p>{{ disease.treatment }}</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Edit Disease Modal -->
                    <div class="modal fade" id="editDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="editDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="editDiseaseModalLabel{{ disease.id }}">Edit Penyakit</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form action="{{ url_for('admin_edit_disease', disease_id=disease.id) }}" method="post">
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label for="name{{ disease.id }}" class="form-label">Nama Penyakit</label>
                                            <input type="text" class="form-control" id="name{{ disease.id }}" name="name" value="{{ disease.name }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="description{{ disease.id }}" class="form-label">Deskripsi</label>
                                            <textarea class="form-control" id="description{{ disease.id }}" name="description" rows="4" required>{{ disease.description }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="symptoms{{ disease.id }}" class="form-label">Gejala</label>
                                            <textarea class="form-control" id="symptoms{{ disease.id }}" name="symptoms" rows="4" required>{{ disease.symptoms }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="treatment{{ disease.id }}" class="form-label">Penanganan</label>
                                            <textarea class="form-control" id="treatment{{ disease.id }}" name="treatment" rows="4" required>{{ disease.treatment }}</textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delete Disease Modal -->
                    <div class="modal fade" id="deleteDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="deleteDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteDiseaseModalLabel{{ disease.id }}">Hapus Penyakit</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Apakah Anda yakin ingin menghapus penyakit <strong>{{ disease.name }}</strong>?</p>
                                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan dan mungkin mempengaruhi hasil klasifikasi.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <a href="{{ url_for('admin_delete_disease', disease_id=disease.id) }}" class="btn btn-danger">Hapus Penyakit</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Disease Modal -->
<div class="modal fade" id="addDiseaseModal" tabindex="-1" aria-labelledby="addDiseaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDiseaseModalLabel">Tambah Penyakit Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_add_disease') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Nama Penyakit</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="symptoms" class="form-label">Gejala</label>
                        <textarea class="form-control" id="symptoms" name="symptoms" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="treatment" class="form-label">Penanganan</label>
                        <textarea class="form-control" id="treatment" name="treatment" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Tambah Penyakit</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
