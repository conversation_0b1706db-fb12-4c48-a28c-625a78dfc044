{% extends "base.html" %}

{% block title %}Tentang - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.about-header {
    text-align: center;
    margin-bottom: 30px;
}
.about-header img {
    width: 120px;
    margin-bottom: 20px;
}
.feature-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    background-color: white;
    margin-bottom: 20px;
    height: 100%;
}
.feature-card i {
    font-size: 40px;
    color: #28a745;
    margin-bottom: 15px;
}
.feature-card h5 {
    margin-bottom: 10px;
}
.team-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    background-color: white;
    margin-bottom: 20px;
    height: 100%;
}
.team-card img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 15px;
}
{% endblock %}

{% block content %}
<div class="about-header">
    <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
    <h2>Tentang PadiKu</h2>
    <p class="lead">Sistem Klasifikasi Penyakit Tanaman Padi Berbasis Web</p>
</div>

<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-info-circle me-2"></i> Deskripsi Sistem
    </div>
    <div class="card-body">
        <p>PadiKu adalah sistem klasifikasi penyakit tanaman padi berbasis web yang menggunakan algoritma Random Forest untuk mengidentifikasi berbagai jenis penyakit pada tanaman padi berdasarkan gambar daun. Sistem ini dikembangkan untuk membantu petani dan peneliti dalam mendiagnosis penyakit tanaman padi dengan cepat dan akurat.</p>
        
        <p>Sistem ini dapat mengklasifikasikan beberapa jenis penyakit umum pada tanaman padi, termasuk:</p>
        <ul>
            <li>Bacterial Leaf Blight (Hawar Daun Bakteri)</li>
            <li>Blast (Blas)</li>
            <li>Brown Spot (Bercak Coklat)</li>
            <li>Tungro</li>
            <li>Tanaman Sehat</li>
        </ul>
        
        <p>Dengan menggunakan sistem ini, pengguna dapat mengunggah gambar daun tanaman padi dan mendapatkan hasil klasifikasi beserta informasi tentang penyakit yang terdeteksi, termasuk deskripsi, gejala, dan cara penanganannya.</p>
    </div>
</div>

<h3 class="mb-4">Fitur Utama</h3>
<div class="row">
    <div class="col-md-4">
        <div class="feature-card">
            <i class="fas fa-microscope"></i>
            <h5>Klasifikasi Penyakit</h5>
            <p>Mengklasifikasikan penyakit tanaman padi berdasarkan gambar daun dengan algoritma Random Forest.</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="feature-card">
            <i class="fas fa-database"></i>
            <h5>Informasi Penyakit</h5>
            <p>Menyediakan informasi lengkap tentang penyakit, termasuk deskripsi, gejala, dan cara penanganan.</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="feature-card">
            <i class="fas fa-history"></i>
            <h5>Riwayat Klasifikasi</h5>
            <p>Menyimpan riwayat klasifikasi untuk referensi dan pemantauan perkembangan penyakit.</p>
        </div>
    </div>
</div>

<h3 class="mb-4 mt-5">Teknologi yang Digunakan</h3>
<div class="row">
    <div class="col-md-3">
        <div class="feature-card">
            <i class="fab fa-python"></i>
            <h5>Python</h5>
            <p>Bahasa pemrograman utama untuk backend dan algoritma machine learning.</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="feature-card">
            <i class="fas fa-flask"></i>
            <h5>Flask</h5>
            <p>Framework web Python yang ringan dan fleksibel.</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="feature-card">
            <i class="fas fa-brain"></i>
            <h5>Scikit-learn</h5>
            <p>Library machine learning untuk implementasi algoritma Random Forest.</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="feature-card">
            <i class="fab fa-bootstrap"></i>
            <h5>Bootstrap</h5>
            <p>Framework CSS untuk desain antarmuka yang responsif.</p>
        </div>
    </div>
</div>

<div class="card mt-5">
    <div class="card-header">
        <i class="fas fa-code me-2"></i> Versi Sistem
    </div>
    <div class="card-body">
        <p><strong>Versi:</strong> 1.0.0</p>
        <p><strong>Tanggal Rilis:</strong> 2023</p>
        <p><strong>Pengembang:</strong> Tim PadiKu</p>
    </div>
</div>
{% endblock %}
