// Fungsi untuk preview gambar saat diunggah
function previewImage(input) {
    var preview = document.getElementById('preview-image');
    var classifyBtn = document.getElementById('classify-btn');
    
    if (input && input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            if (classifyBtn) {
                classifyBtn.disabled = false;
            }
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        if (preview) {
            preview.style.display = 'none';
        }
        if (classifyBtn) {
            classifyBtn.disabled = true;
        }
    }
}

// Fungsi untuk memuat informasi penyakit
function loadDiseaseInfo(diseaseName, elementId) {
    fetch(`/api/disease_info?name=${diseaseName}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                var diseaseInfo = document.getElementById(elementId);
                if (diseaseInfo) {
                    diseaseInfo.innerHTML = `
                        <p><strong>Deskripsi:</strong> ${data.disease.description}</p>
                        <p><strong>Gejala:</strong> ${data.disease.symptoms}</p>
                        <p><strong>Penanganan:</strong> ${data.disease.treatment}</p>
                    `;
                }
            } else {
                var diseaseInfo = document.getElementById(elementId);
                if (diseaseInfo) {
                    diseaseInfo.innerHTML = `<p class="text-danger">Informasi penyakit tidak ditemukan.</p>`;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            var diseaseInfo = document.getElementById(elementId);
            if (diseaseInfo) {
                diseaseInfo.innerHTML = `<p class="text-danger">Terjadi kesalahan saat memuat informasi penyakit.</p>`;
            }
        });
}

// Fungsi untuk memuat informasi model
function loadModelInfo() {
    fetch('/api/model_info')
        .then(response => response.json())
        .then(data => {
            var modelStatus = document.getElementById('model-status');
            var modelDate = document.getElementById('model-date');
            var modelAccuracy = document.getElementById('model-accuracy');
            var modelSamples = document.getElementById('model-samples');
            var modelClasses = document.getElementById('model-classes');
            var confusionMatrix = document.getElementById('confusion-matrix');
            var confusionMatrixContainer = document.getElementById('confusion-matrix-container');
            
            if (!modelStatus || !modelDate || !modelAccuracy || !modelSamples || !modelClasses) {
                return;
            }
            
            if (data.success) {
                modelStatus.textContent = 'Model tersedia';
                modelStatus.className = 'text-success';
                modelDate.textContent = data.info.timestamp || 'Tidak tersedia';
                modelAccuracy.textContent = data.info.accuracy ? data.info.accuracy.toFixed(2) + '%' : 'Tidak tersedia';
                modelSamples.textContent = data.info.n_samples || 'Tidak tersedia';
                modelClasses.textContent = data.info.classes ? data.info.classes.join(', ') : 'Tidak tersedia';
                
                // Check if confusion matrix exists
                if (confusionMatrix) {
                    confusionMatrix.onload = function() {
                        this.style.display = 'inline-block';
                    }
                    confusionMatrix.onerror = function() {
                        this.style.display = 'none';
                        if (confusionMatrixContainer) {
                            confusionMatrixContainer.innerHTML = '<p>Confusion Matrix belum tersedia. Latih model terlebih dahulu.</p>';
                        }
                    }
                    confusionMatrix.src = '/static/models/confusion_matrix.png?' + new Date().getTime();
                }
            } else {
                modelStatus.textContent = 'Model belum tersedia';
                modelStatus.className = 'text-danger';
                modelDate.textContent = 'Tidak tersedia';
                modelAccuracy.textContent = 'Tidak tersedia';
                modelSamples.textContent = 'Tidak tersedia';
                modelClasses.textContent = 'Tidak tersedia';
                if (confusionMatrix) {
                    confusionMatrix.style.display = 'none';
                }
                if (confusionMatrixContainer) {
                    confusionMatrixContainer.innerHTML = '<p>Confusion Matrix belum tersedia. Latih model terlebih dahulu.</p>';
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            var modelStatus = document.getElementById('model-status');
            if (modelStatus) {
                modelStatus.textContent = 'Error saat memuat informasi model';
                modelStatus.className = 'text-danger';
            }
        });
}

// Inisialisasi saat dokumen dimuat
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi upload area jika ada
    var uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#e9ecef';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';
            
            var file = e.dataTransfer.files[0];
            var input = document.getElementById('image') || document.getElementById('dataset');
            
            if (input && file) {
                // Create a new FileList object
                var dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                input.files = dataTransfer.files;
                
                if (input.id === 'image') {
                    previewImage(input);
                } else if (input.id === 'dataset') {
                    updateFileName(input);
                }
            }
        });
    }
    
    // Inisialisasi refresh model info button jika ada
    var refreshInfoBtn = document.getElementById('refresh-info-btn');
    if (refreshInfoBtn) {
        refreshInfoBtn.addEventListener('click', loadModelInfo);
        // Load model info on page load
        loadModelInfo();
    }
    
    // Inisialisasi modal untuk detail klasifikasi jika ada
    var detailModals = document.querySelectorAll('[id^="detailModal"]');
    detailModals.forEach(function(modal) {
        modal.addEventListener('shown.bs.modal', function() {
            var modalId = modal.id;
            var classificationId = modalId.replace('detailModal', '');
            var predictionElement = document.querySelector(`#detailModal${classificationId} .prediction`);
            if (predictionElement) {
                var prediction = predictionElement.textContent;
                loadDiseaseInfo(prediction, `disease-info-${classificationId}`);
            }
        });
    });
});
