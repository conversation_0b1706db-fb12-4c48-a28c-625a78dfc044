{"timestamp": "2025-06-03 17:49:47", "accuracy": 85.64356435643565, "n_samples": 1010, "test_samples": 202, "n_features": 150528, "classes": ["Bacterial Leaf Blight", "Blast", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "class_metrics": {"Bacterial Leaf Blight": {"precision": 1.0, "recall": 0.7878787878787878, "f1": 0.8813559322033898, "support": 33}, "Blast": {"precision": 0.8571428571428571, "recall": 0.8571428571428571, "f1": 0.8571428571428571, "support": 49}, "Brown Spot": {"precision": 0.8666666666666667, "recall": 0.9069767441860465, "f1": 0.8863636363636364, "support": 43}, "Sehat": {"precision": 0.8974358974358975, "recall": 0.9722222222222222, "f1": 0.9333333333333333, "support": 36}, "Tungro": {"precision": 0.7209302325581395, "recall": 0.7560975609756098, "f1": 0.7380952380952381, "support": 41}}, "classification_report": "                       precision    recall  f1-score   support\n\nBacterial Leaf Blight       1.00      0.79      0.88        33\n                Blast       0.86      0.86      0.86        49\n           Brown Spot       0.87      0.91      0.89        43\n                Sehat       0.90      0.97      0.93        36\n               Tungro       0.72      0.76      0.74        41\n\n             accuracy                           0.86       202\n            macro avg       0.87      0.86      0.86       202\n         weighted avg       0.86      0.86      0.86       202\n", "examples": [{"true_label": "<PERSON><PERSON>", "predicted_label": "<PERSON><PERSON>", "confidence": 62.0, "image": "example_0.png"}, {"true_label": "<PERSON><PERSON>", "predicted_label": "<PERSON><PERSON>", "confidence": 73.0, "image": "example_1.png"}, {"true_label": "<PERSON><PERSON>", "predicted_label": "<PERSON><PERSON>", "confidence": 95.0, "image": "example_2.png"}, {"true_label": "Blast", "predicted_label": "<PERSON><PERSON><PERSON>", "confidence": 59.0, "image": "example_3.png"}, {"true_label": "Bacterial Leaf Blight", "predicted_label": "<PERSON><PERSON><PERSON>", "confidence": 36.0, "image": "example_4.png"}, {"true_label": "<PERSON><PERSON><PERSON>", "predicted_label": "Blast", "confidence": 62.0, "image": "example_5.png"}], "model_params": {"n_estimators": 100, "max_depth": "None", "min_samples_split": 2, "min_samples_leaf": 1}}