<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>ifikasi - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .result-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .result-box {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .result-icon {
            font-size: 60px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .result-disease {
            font-size: 24px;
            font-weight: 700;
            color: #343a40;
            margin-bottom: 10px;
        }
        .result-confidence {
            font-size: 18px;
            color: #6c757d;
        }
        .progress {
            height: 25px;
            margin-bottom: 15px;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}" class="active">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>
        
        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <h2 class="mb-4">Hasil Klasifikasi Penyakit</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-image me-2"></i> Gambar Tanaman Padi
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ url_for('static', filename='uploads/' + image_path) }}" alt="Gambar Tanaman Padi" class="result-image">
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-chart-pie me-2"></i> Hasil Klasifikasi
                        </div>
                        <div class="card-body">
                            <div class="result-box">
                                <i class="fas fa-leaf result-icon"></i>
                                <div class="result-disease">{{ result.prediction }}</div>
                                <div class="result-confidence">Tingkat Kepercayaan: {{ "%.2f"|format(result.confidence) }}%</div>
                            </div>
                            
                            <h5 class="mt-4 mb-3">Probabilitas Kelas</h5>
                            <canvas id="probabilityChart"></canvas>
                            
                            <div class="mt-4">
                                <a href="{{ url_for('classification') }}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i> Klasifikasi Gambar Lain
                                </a>
                                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-home me-2"></i> Kembali ke Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> Informasi Penyakit
                </div>
                <div class="card-body">
                    <div id="disease-info">
                        <div class="text-center">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Memuat informasi penyakit...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Data untuk chart probabilitas
        var ctx = document.getElementById('probabilityChart').getContext('2d');
        var probabilities = {{ result.probabilities|tojson }};
        
        var labels = [];
        var data = [];
        var backgroundColors = [];
        
        // Generate random colors for each class
        function getRandomColor() {
            var letters = '0123456789ABCDEF';
            var color = '#';
            for (var i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 16)];
            }
            return color;
        }
        
        // Prepare data for chart
        for (var key in probabilities) {
            labels.push(key);
            data.push(probabilities[key]);
            backgroundColors.push(key === "{{ result.prediction }}" ? '#28a745' : getRandomColor());
        }
        
        // Create chart
        var myChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Probabilitas (%)',
                    data: data,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Fetch disease information
        fetch('/api/disease_info?name={{ result.prediction }}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    var diseaseInfo = document.getElementById('disease-info');
                    diseaseInfo.innerHTML = `
                        <h4>${data.disease.name}</h4>
                        <p><strong>Deskripsi:</strong> ${data.disease.description}</p>
                        <p><strong>Gejala:</strong> ${data.disease.symptoms}</p>
                        <p><strong>Penanganan:</strong> ${data.disease.treatment}</p>
                    `;
                } else {
                    var diseaseInfo = document.getElementById('disease-info');
                    diseaseInfo.innerHTML = `<p class="text-danger">Informasi penyakit tidak ditemukan.</p>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                var diseaseInfo = document.getElementById('disease-info');
                diseaseInfo.innerHTML = `<p class="text-danger">Terjadi kesalahan saat memuat informasi penyakit.</p>`;
            });
    </script>
</body>
</html>
