{% extends "base.html" %}

{% block title %}Evaluasi Model - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.metric-card {
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    background-color: white;
    margin-bottom: 20px;
}
.metric-icon {
    font-size: 40px;
    color: #28a745;
    margin-right: 20px;
}
.metric-title {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}
.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #343a40;
}
.classification-report {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 14px;
}
.example-image {
    height: 150px;
    object-fit: cover;
}
.prediction-correct {
    color: #28a745;
    font-weight: 600;
}
.prediction-incorrect {
    color: #dc3545;
    font-weight: 600;
}
{% endblock %}

{% block content %}
<h2 class="mb-4"><i class="fas fa-chart-bar me-2"></i> Evaluasi Model</h2>

<div class="row">
    <div class="col-md-4">
        <div class="metric-card">
            <div class="d-flex align-items-center">
                <i class="fas fa-flask metric-icon"></i>
                <div>
                    <div class="metric-title">Nama Model</div>
                    <div class="metric-value" id="model-name">Random Forest</div>
                    <small id="model-params">n_estimators=100, max_depth=None</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="metric-card">
            <div class="d-flex align-items-center">
                <i class="fas fa-chart-line metric-icon"></i>
                <div>
                    <div class="metric-title">Akurasi Model</div>
                    <div class="metric-value" id="model-accuracy">{{ model_info.accuracy|round(2) }}%</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="metric-card">
            <div class="d-flex align-items-center">
                <i class="fas fa-box metric-icon"></i>
                <div>
                    <div class="metric-title">Jumlah Data Uji</div>
                    <div class="metric-value" id="test-data-count">{{ model_info.test_samples }}</div>
                    <small>dari total {{ model_info.n_samples }} sampel</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i> Confusion Matrix
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='models/confusion_matrix.png') }}?t={{ model_info.timestamp }}" alt="Confusion Matrix" class="img-fluid" style="max-width: 100%;">
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clipboard-list me-2"></i> Precision / Recall / F1
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Kelas</th>
                                <th>Precision</th>
                                <th>Recall</th>
                                <th>F1-Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for class_name, metrics in model_info.class_metrics.items() %}
                            <tr>
                                <td>{{ class_name }}</td>
                                <td>{{ metrics.precision|round(2) }}</td>
                                <td>{{ metrics.recall|round(2) }}</td>
                                <td>{{ metrics.f1|round(2) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-file-alt me-2"></i> Classification Report
    </div>
    <div class="card-body">
        <pre class="classification-report">{{ model_info.classification_report }}</pre>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-calendar-alt me-2"></i> Informasi Training
    </div>
    <div class="card-body">
        <p><strong>Tanggal/Waktu Training:</strong> {{ model_info.timestamp }}</p>
        <p><strong>Jumlah Fitur:</strong> {{ model_info.n_features }}</p>
        <p><strong>Kelas:</strong> {{ model_info.classes|join(', ') }}</p>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-images me-2"></i> Contoh Prediksi
    </div>
    <div class="card-body">
        <div class="row">
            {% for example in model_info.examples %}
            <div class="col-md-4 mb-4">
                <div class="card">
                    <img src="{{ url_for('static', filename='models/examples/' + example.image) }}" class="card-img-top example-image" alt="Example">
                    <div class="card-body">
                        <p class="mb-1"><strong>Label Asli:</strong> {{ example.true_label }}</p>
                        <p class="mb-1">
                            <strong>Prediksi:</strong> 
                            <span class="{{ 'prediction-correct' if example.true_label == example.predicted_label else 'prediction-incorrect' }}">
                                {{ example.predicted_label }}
                            </span>
                        </p>
                        <p class="mb-0"><strong>Confidence:</strong> {{ example.confidence|round(2) }}%</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
