{% extends "base.html" %}

{% block title %}Kelola Pengguna - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.admin-badge {
    background-color: #dc3545;
}
.user-badge {
    background-color: #17a2b8;
}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Kelola Pengguna</h2>
    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
        <i class="fas fa-user-plus me-2"></i> Tambah Pengguna
    </button>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-users me-2"></i> Daftar Pengguna
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th><PERSON><PERSON></th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Alamat</th>
                        <th>Role</th>
                        <th>Tanggal Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.full_name|default('-') }}</td>
                        <td>
                            <img src="https://ui-avatars.com/api/?name={{ user.full_name|default(user.username) }}&background=random&size=30" alt="User" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                            {{ user.username }}
                        </td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.address|default('-') }}</td>
                        <td>
                            <span class="badge {% if user.role == 'admin' %}admin-badge{% else %}user-badge{% endif %}">
                                {{ user.role }}
                            </span>
                        </td>
                        <td>{{ user.created_at }}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editUserModal{{ user.id }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal{{ user.id }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>

                    <!-- Edit User Modal -->
                    <div class="modal fade" id="editUserModal{{ user.id }}" tabindex="-1" aria-labelledby="editUserModalLabel{{ user.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="editUserModalLabel{{ user.id }}">Edit Pengguna</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form action="{{ url_for('admin_edit_user', user_id=user.id) }}" method="post">
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label for="full_name{{ user.id }}" class="form-label">Nama Lengkap</label>
                                            <input type="text" class="form-control" id="full_name{{ user.id }}" name="full_name" value="{{ user.full_name }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="email{{ user.id }}" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email{{ user.id }}" name="email" value="{{ user.email }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="address{{ user.id }}" class="form-label">Alamat/Wilayah Pertanian</label>
                                            <input type="text" class="form-control" id="address{{ user.id }}" name="address" value="{{ user.address }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="username{{ user.id }}" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username{{ user.id }}" name="username" value="{{ user.username }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="password{{ user.id }}" class="form-label">Password (Kosongkan jika tidak ingin mengubah)</label>
                                            <input type="password" class="form-control" id="password{{ user.id }}" name="password">
                                        </div>
                                        <div class="mb-3">
                                            <label for="role{{ user.id }}" class="form-label">Role</label>
                                            <select class="form-select" id="role{{ user.id }}" name="role" required>
                                                <option value="user" {% if user.role == 'user' %}selected{% endif %}>User</option>
                                                <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Delete User Modal -->
                    <div class="modal fade" id="deleteUserModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteUserModalLabel{{ user.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteUserModalLabel{{ user.id }}">Hapus Pengguna</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Apakah Anda yakin ingin menghapus pengguna <strong>{{ user.username }}</strong>?</p>
                                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait pengguna ini.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <a href="{{ url_for('admin_delete_user', user_id=user.id) }}" class="btn btn-danger">Hapus Pengguna</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Tambah Pengguna Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_add_user') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Alamat/Wilayah Pertanian</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user" selected>User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Tambah Pengguna</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
