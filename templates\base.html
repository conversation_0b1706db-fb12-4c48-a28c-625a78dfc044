<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    {% block extra_css %}{% endblock %}
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
            z-index: 100;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
            padding-top: 70px; /* Add padding for topbar */
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        /* Topbar styles */
        .topbar {
            position: fixed;
            top: 0;
            right: 0;
            left: 250px;
            height: 60px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 20px;
            z-index: 99;
        }
        .user-dropdown {
            position: relative;
            cursor: pointer;
        }
        .user-dropdown img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
            min-width: 200px;
        }
        .dropdown-menu.show {
            display: block;
        }
        .dropdown-item {
            padding: 10px 15px;
            display: flex;
            align-items: center;
            color: #333;
            text-decoration: none;
        }
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
        .dropdown-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        {% block extra_style %}{% endblock %}
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}" {% if request.endpoint == 'dashboard' %}class="active"{% endif %}>
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}" {% if request.endpoint == 'classification' %}class="active"{% endif %}>
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('classification_results') }}" {% if request.endpoint == 'classification_results' %}class="active"{% endif %}>
                <i class="fas fa-history"></i> Hasil Klasifikasi
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}" {% if request.endpoint == 'admin_users' %}class="active"{% endif %}>
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_diseases') }}" {% if request.endpoint == 'admin_diseases' %}class="active"{% endif %}>
                <i class="fas fa-virus"></i> Kelola Penyakit
            </a>
            <a href="{{ url_for('admin_train') }}" {% if request.endpoint == 'admin_train' %}class="active"{% endif %}>
                <i class="fas fa-brain"></i> Latih Model
            </a>
            <a href="{{ url_for('admin_evaluate') }}" {% if request.endpoint == 'admin_evaluate' %}class="active"{% endif %}>
                <i class="fas fa-chart-bar"></i> Evaluasi Model
            </a>
            {% endif %}
            <a href="{{ url_for('about') }}" {% if request.endpoint == 'about' %}class="active"{% endif %}>
                <i class="fas fa-info-circle"></i> Tentang
            </a>
        </div>
    </div>

    <!-- Topbar -->
    <div class="topbar">
        <div class="user-dropdown" id="userDropdown">
            <div class="d-flex align-items-center">
                <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
                <span>{{ current_user.username }} <i class="fas fa-chevron-down ms-2"></i></span>
            </div>
            <div class="dropdown-menu" id="userDropdownMenu">
                <a href="{{ url_for('profile') }}" class="dropdown-item">
                    <i class="fas fa-user"></i> Profil
                </a>
                <a href="{{ url_for('logout') }}" class="dropdown-item">
                    <i class="fas fa-sign-out-alt"></i> Keluar
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // User dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const userDropdown = document.getElementById('userDropdown');
            const userDropdownMenu = document.getElementById('userDropdownMenu');

            userDropdown.addEventListener('click', function(e) {
                userDropdownMenu.classList.toggle('show');
                e.stopPropagation();
            });

            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target)) {
                    userDropdownMenu.classList.remove('show');
                }
            });
        });

        {% block extra_scripts %}{% endblock %}
    </script>
</body>
</html>
