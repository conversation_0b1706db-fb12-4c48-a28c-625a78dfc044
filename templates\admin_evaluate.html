<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evaluasi Model - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .metric-card {
            border-left: 4px solid #28a745;
            background-color: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .metric-icon {
            font-size: 24px;
            color: #28a745;
            margin-right: 10px;
        }
        .metric-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 18px;
            font-weight: 700;
            color: #212529;
        }
        .table-responsive {
            margin-bottom: 20px;
        }
        .example-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .prediction-correct {
            color: #28a745;
            font-weight: 600;
        }
        .prediction-incorrect {
            color: #dc3545;
            font-weight: 600;
        }
        .classification-report {
            font-family: monospace;
            white-space: pre-wrap;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            <a href="{{ url_for('admin_evaluate') }}" class="active">
                <i class="fas fa-chart-bar"></i> Evaluasi Model
            </a>
            <a href="{{ url_for('admin_diseases') }}">
                <i class="fas fa-virus"></i> Kelola Penyakit
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>

        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <h2 class="mb-4"><i class="fas fa-chart-bar me-2"></i> Evaluasi Model</h2>

            <div class="row">
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-flask metric-icon"></i>
                            <div>
                                <div class="metric-title">Nama Model</div>
                                <div class="metric-value" id="model-name">Random Forest</div>
                                <small id="model-params">n_estimators=100, max_depth=None</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-chart-line metric-icon"></i>
                            <div>
                                <div class="metric-title">Akurasi Model</div>
                                <div class="metric-value" id="model-accuracy">{{ model_info.accuracy|round(2) }}%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-box metric-icon"></i>
                            <div>
                                <div class="metric-title">Jumlah Data Uji</div>
                                <div class="metric-value" id="test-data-count">{{ model_info.test_samples }}</div>
                                <small>dari total {{ model_info.n_samples }} sampel</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-chart-bar me-2"></i> Confusion Matrix
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ url_for('static', filename='models/confusion_matrix.png') }}?t={{ model_info.timestamp }}" alt="Confusion Matrix" class="img-fluid" style="max-width: 100%;">
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-clipboard-list me-2"></i> Precision / Recall / F1
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Kelas</th>
                                            <th>Precision</th>
                                            <th>Recall</th>
                                            <th>F1-Score</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for class_name, metrics in model_info.class_metrics.items() %}
                                        <tr>
                                            <td>{{ class_name }}</td>
                                            <td>{{ metrics.precision|round(2) }}</td>
                                            <td>{{ metrics.recall|round(2) }}</td>
                                            <td>{{ metrics.f1|round(2) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-file-alt me-2"></i> Classification Report
                </div>
                <div class="card-body">
                    <pre class="classification-report">{{ model_info.classification_report }}</pre>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-calendar-alt me-2"></i> Informasi Training
                </div>
                <div class="card-body">
                    <p><strong>Tanggal/Waktu Training:</strong> {{ model_info.timestamp }}</p>
                    <p><strong>Jumlah Fitur:</strong> {{ model_info.n_features }}</p>
                    <p><strong>Kelas:</strong> {{ model_info.classes|join(', ') }}</p>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-images me-2"></i> Contoh Prediksi
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for example in model_info.examples %}
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <img src="{{ url_for('static', filename='models/examples/' + example.image) }}" class="card-img-top example-image" alt="Example">
                                <div class="card-body">
                                    <p class="mb-1"><strong>Label Asli:</strong> {{ example.true_label }}</p>
                                    <p class="mb-1">
                                        <strong>Prediksi:</strong>
                                        <span class="{{ 'prediction-correct' if example.true_label == example.predicted_label else 'prediction-incorrect' }}">
                                            {{ example.predicted_label }}
                                        </span>
                                    </p>
                                    <p class="mb-0"><strong>Confidence:</strong> {{ example.confidence|round(2) }}%</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
