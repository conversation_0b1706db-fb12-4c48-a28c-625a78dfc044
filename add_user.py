import sqlite3
import os
from werkzeug.security import generate_password_hash

# Pastikan database ada
db_path = 'database/padiku.db'
if not os.path.exists(db_path):
    print(f"Database tidak ditemukan di {db_path}")
    exit(1)

# Buat koneksi ke database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Tambahkan pengguna admin baru
username = 'admin2'
email = '<EMAIL>'
password = 'admin123'
role = 'admin'

# Hash password
hashed_password = generate_password_hash(password)

try:
    # Cek apakah username sudah ada
    cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
    if cursor.fetchone():
        print(f"Username {username} sudah ada")
    else:
        # Tambahkan pengguna baru
        cursor.execute('''
            INSERT INTO users (username, email, password, role)
            VALUES (?, ?, ?, ?)
        ''', (username, email, hashed_password, role))
        conn.commit()
        print(f"Pengguna {username} berhasil ditambahkan")
        
    # <PERSON><PERSON><PERSON><PERSON> semua pengguna
    print("\nDaftar pengguna:")
    cursor.execute('SELECT id, username, email, role FROM users')
    for user in cursor.fetchall():
        print(f"ID: {user[0]}, Username: {user[1]}, Email: {user[2]}, Role: {user[3]}")
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    conn.close()
