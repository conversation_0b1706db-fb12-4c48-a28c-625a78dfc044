<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .profile-header {
            text-align: center;
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .profile-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin-bottom: 20px;
            object-fit: cover;
        }
        .profile-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .profile-role {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 15px;
        }
        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #28a745;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant-simple.svg') }}" alt="Logo" style="width: 60px; height: 60px;">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}" class="active">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>

        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="profile-header">
                <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random&size=150" alt="Profile" class="profile-image">
                <div class="profile-name">{{ current_user.username }}</div>
                <div class="profile-role">{{ current_user.role|title }}</div>
                <div class="profile-email">{{ current_user.email }}</div>

                <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                    <i class="fas fa-edit me-2"></i> Edit Profil
                </button>

                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ classifications|length }}</div>
                        <div class="stat-label">Klasifikasi</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ classifications|selectattr('prediction', 'equalto', 'Sehat')|list|length }}</div>
                        <div class="stat-label">Tanaman Sehat</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ classifications|rejectattr('prediction', 'equalto', 'Sehat')|list|length }}</div>
                        <div class="stat-label">Tanaman Terinfeksi</div>
                    </div>
                </div>
            </div>

            <!-- Modal Edit Profil -->
            <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editProfileModalLabel">Edit Informasi Pribadi</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form method="post" action="{{ url_for('edit_profile') }}">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Password Saat Ini</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" placeholder="Masukkan password saat ini untuk verifikasi" required>
                                </div>
                                <hr>
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">Password Baru (kosongkan jika tidak ingin mengubah)</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                </div>
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history me-2"></i> Riwayat Klasifikasi
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Gambar</th>
                                    <th>Prediksi</th>
                                    <th>Kepercayaan</th>
                                    <th>Tanggal</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for classification in classifications %}
                                <tr>
                                    <td>{{ classification.id }}</td>
                                    <td>
                                        <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                             alt="Gambar Tanaman" class="img-thumbnail" style="width: 50px;">
                                    </td>
                                    <td>
                                        <span class="badge {% if classification.prediction == 'Sehat' %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ classification.prediction }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                                    <td>{{ classification.created_at }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#detailModal{{ classification.id }}">
                                            <i class="fas fa-eye"></i> Detail
                                        </button>
                                    </td>
                                </tr>

                                <!-- Detail Modal -->
                                <div class="modal fade" id="detailModal{{ classification.id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ classification.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="detailModalLabel{{ classification.id }}">Detail Klasifikasi #{{ classification.id }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6 text-center">
                                                        <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                                             alt="Gambar Tanaman" class="img-fluid rounded" style="max-height: 300px;">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h5>Hasil Klasifikasi</h5>
                                                        <p><strong>Prediksi:</strong> {{ classification.prediction }}</p>
                                                        <p><strong>Kepercayaan:</strong> {{ "%.2f"|format(classification.confidence) }}%</p>
                                                        <p><strong>Tanggal:</strong> {{ classification.created_at }}</p>

                                                        <h5 class="mt-4">Informasi Penyakit</h5>
                                                        <div id="disease-info-{{ classification.id }}">
                                                            <div class="spinner-border text-success" role="status">
                                                                <span class="visually-hidden">Loading...</span>
                                                            </div>
                                                            <p>Memuat informasi penyakit...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">Belum ada riwayat klasifikasi</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Fungsi untuk memuat informasi penyakit
        function loadDiseaseInfo(diseaseName, elementId) {
            fetch(`/api/disease_info?name=${diseaseName}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        var diseaseInfo = document.getElementById(elementId);
                        diseaseInfo.innerHTML = `
                            <p><strong>Deskripsi:</strong> ${data.disease.description}</p>
                            <p><strong>Gejala:</strong> ${data.disease.symptoms}</p>
                            <p><strong>Penanganan:</strong> ${data.disease.treatment}</p>
                        `;
                    } else {
                        var diseaseInfo = document.getElementById(elementId);
                        diseaseInfo.innerHTML = `<p class="text-danger">Informasi penyakit tidak ditemukan.</p>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    var diseaseInfo = document.getElementById(elementId);
                    diseaseInfo.innerHTML = `<p class="text-danger">Terjadi kesalahan saat memuat informasi penyakit.</p>`;
                });
        }

        // Memuat informasi penyakit saat modal dibuka
        {% for classification in classifications %}
        document.getElementById('detailModal{{ classification.id }}').addEventListener('shown.bs.modal', function () {
            loadDiseaseInfo('{{ classification.prediction }}', 'disease-info-{{ classification.id }}');
        });
        {% endfor %}

        // Validasi form edit profil
        document.addEventListener('DOMContentLoaded', function() {
            const editProfileForm = document.querySelector('#editProfileModal form');
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');

            if (editProfileForm) {
                editProfileForm.addEventListener('submit', function(e) {
                    // Validasi password baru dan konfirmasi password
                    if (newPasswordInput.value && newPasswordInput.value !== confirmPasswordInput.value) {
                        e.preventDefault();
                        alert('Password baru dan konfirmasi password tidak cocok!');
                        return false;
                    }

                    // Validasi password baru minimal 6 karakter
                    if (newPasswordInput.value && newPasswordInput.value.length < 6) {
                        e.preventDefault();
                        alert('Password baru harus minimal 6 karakter!');
                        return false;
                    }

                    return true;
                });
            }
        });
    </script>
</body>
</html>
