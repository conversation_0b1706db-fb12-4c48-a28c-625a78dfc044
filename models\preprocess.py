import os
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def resize_image(image, target_size=(224, 224)):
    """
    Resize gambar ke ukuran target
    """
    return cv2.resize(image, target_size)

def normalize_image(image):
    """
    Normalisasi nilai pixel gambar ke range [0, 1]
    """
    return image / 255.0

def load_and_preprocess_image(image_path, target_size=(224, 224)):
    """
    Muat dan preprocess gambar dari path
    """
    try:
        # Baca gambar
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Tidak dapat membaca gambar dari {image_path}")
        
        # Resize
        img = resize_image(img, target_size)
        
        # Konversi ke RGB (OpenCV membaca dalam format BGR)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Normalisasi
        img = normalize_image(img)
        
        return img
    
    except Exception as e:
        print(f"Error saat memproses gambar {image_path}: {str(e)}")
        return None

def augment_image(image, save_path=None, rotation_range=15, flip=True, brightness_range=(0.8, 1.2)):
    """
    Melakukan augmentasi sederhana pada gambar
    """
    augmented_images = []
    
    # Konversi ke PIL Image untuk augmentasi
    if isinstance(image, np.ndarray):
        image_pil = Image.fromarray((image * 255).astype(np.uint8) if image.max() <= 1.0 else image.astype(np.uint8))
    else:
        image_pil = image
    
    # Rotasi
    if rotation_range > 0:
        for angle in [-rotation_range, rotation_range]:
            rotated = image_pil.rotate(angle)
            augmented_images.append(np.array(rotated))
    
    # Flip horizontal
    if flip:
        flipped = image_pil.transpose(Image.FLIP_LEFT_RIGHT)
        augmented_images.append(np.array(flipped))
    
    # Kecerahan
    if brightness_range:
        for factor in [brightness_range[0], brightness_range[1]]:
            enhancer = Image.ImageEnhance.Brightness(image_pil)
            brightened = enhancer.enhance(factor)
            augmented_images.append(np.array(brightened))
    
    # Simpan gambar hasil augmentasi jika path disediakan
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        
        # Simpan gambar asli
        original_path = os.path.join(save_path, "original.jpg")
        image_pil.save(original_path)
        
        # Simpan gambar hasil augmentasi
        for i, aug_img in enumerate(augmented_images):
            aug_path = os.path.join(save_path, f"augmented_{i+1}.jpg")
            Image.fromarray(aug_img).save(aug_path)
    
    return augmented_images

def visualize_preprocessing(image_path, save_path=None):
    """
    Visualisasikan langkah-langkah preprocessing pada gambar
    """
    # Baca gambar asli
    original = cv2.imread(image_path)
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    
    # Resize
    resized = resize_image(original)
    resized_rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    
    # Normalisasi
    normalized = normalize_image(resized_rgb)
    
    # Konversi ke grayscale
    gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
    
    # Visualisasi
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 2, 1)
    plt.title("Original")
    plt.imshow(original_rgb)
    plt.axis('off')
    
    plt.subplot(2, 2, 2)
    plt.title(f"Resized ({resized.shape[1]}x{resized.shape[0]})")
    plt.imshow(resized_rgb)
    plt.axis('off')
    
    plt.subplot(2, 2, 3)
    plt.title("Normalized")
    plt.imshow(normalized)
    plt.axis('off')
    
    plt.subplot(2, 2, 4)
    plt.title("Grayscale")
    plt.imshow(gray, cmap='gray')
    plt.axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Visualisasi disimpan di {save_path}")
    else:
        plt.show()

def create_dataset_from_directory(input_dir, output_dir, target_size=(224, 224), augment=True):
    """
    Membuat dataset terpreprocess dari direktori input
    Format direktori yang diharapkan:
    input_dir/
        class1/
            image1.jpg
            image2.jpg
            ...
        class2/
            image1.jpg
            ...
        ...
    """
    # Buat direktori output jika belum ada
    os.makedirs(output_dir, exist_ok=True)
    
    # Iterasi melalui setiap kelas (subdirektori)
    for class_name in os.listdir(input_dir):
        class_dir = os.path.join(input_dir, class_name)
        
        if not os.path.isdir(class_dir):
            continue
        
        print(f"Memproses kelas: {class_name}")
        
        # Buat direktori output untuk kelas ini
        output_class_dir = os.path.join(output_dir, class_name)
        os.makedirs(output_class_dir, exist_ok=True)
        
        # Iterasi melalui setiap gambar dalam kelas
        for image_name in os.listdir(class_dir):
            if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue
                
            image_path = os.path.join(class_dir, image_name)
            
            # Preprocess gambar
            processed_img = load_and_preprocess_image(image_path, target_size)
            
            if processed_img is None:
                continue
            
            # Simpan gambar yang telah dipreprocess
            output_path = os.path.join(output_class_dir, image_name)
            cv2.imwrite(output_path, cv2.cvtColor((processed_img * 255).astype(np.uint8), cv2.COLOR_RGB2BGR))
            
            # Augmentasi jika diperlukan
            if augment:
                augmented_images = augment_image(processed_img)
                
                for i, aug_img in enumerate(augmented_images):
                    aug_name = f"{os.path.splitext(image_name)[0]}_aug{i+1}{os.path.splitext(image_name)[1]}"
                    aug_path = os.path.join(output_class_dir, aug_name)
                    cv2.imwrite(aug_path, cv2.cvtColor(aug_img, cv2.COLOR_RGB2BGR))
    
    print(f"Dataset terpreprocess disimpan di {output_dir}")

# Jika file ini dijalankan langsung
if __name__ == "__main__":
    # Contoh penggunaan
    print("Modul preprocessing gambar untuk klasifikasi penyakit tanaman padi")
    print("Gunakan fungsi-fungsi dalam modul ini untuk preprocessing dataset Anda")
