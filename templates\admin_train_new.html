{% extends "base.html" %}

{% block title %}Latih Model - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}
.upload-area:hover {
    background-color: #e9ecef;
}
.upload-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}
.train-icon {
    font-size: 50px;
    color: #007bff;
    margin-bottom: 15px;
}
.model-info {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}
.model-info-item {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}
.model-info-label {
    font-weight: 600;
}
{% endblock %}

{% block content %}
<h2 class="mb-4">Latih Model Random Forest</h2>

<!-- Tab Navigation -->
<ul class="nav nav-tabs mb-4" id="modelTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link active" id="disease-tab" data-bs-toggle="tab" href="#disease" role="tab" aria-controls="disease" aria-selected="true">
            <i class="fas fa-leaf me-2"></i>Model Klasifikasi Penyakit
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" href="{{ url_for('admin_rice_detector') }}">
            <i class="fas fa-seedling me-2"></i>Model Deteksi Tanaman Padi
        </a>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="modelTabsContent">
    <!-- Disease Classification Tab -->
    <div class="tab-pane fade show active" id="disease" role="tabpanel" aria-labelledby="disease-tab">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-upload me-2"></i> Unggah Dataset Penyakit Padi
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('admin_upload_dataset') }}" method="post" enctype="multipart/form-data">
                            <div class="upload-area" id="upload-area" onclick="document.getElementById('dataset').click();">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5>Klik atau seret file dataset ke sini</h5>
                                <p class="text-muted">Format yang didukung: ZIP (Maks. 100MB)</p>
                                <input type="file" id="dataset" name="dataset" accept=".zip" style="display: none;" onchange="updateFileName(this);">
                                <p id="selected-file" class="mt-3"></p>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg" id="upload-btn" disabled>
                                    <i class="fas fa-upload me-2"></i> Unggah Dataset
                                </button>
                            </div>
                        </form>

                <div class="mt-4">
                    <h5>Struktur Dataset yang Diharapkan:</h5>
                    <pre class="bg-light p-3 rounded">
dataset.zip
├── Blast/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Brown_Spot/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Bacterial_Leaf_Blight/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Tungro/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── Sehat/
    ├── image1.jpg
    ├── image2.jpg
    └── ...
                    </pre>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-brain me-2"></i> Latih Model
            </div>
            <div class="card-body text-center">
                <i class="fas fa-cogs train-icon"></i>
                <h4 class="mb-3">Latih Model Random Forest</h4>
                <p>Latih model Random Forest dengan dataset yang telah diunggah. Proses pelatihan mungkin memakan waktu beberapa menit tergantung ukuran dataset.</p>

                <form action="{{ url_for('admin_train') }}" method="post">
                    <input type="hidden" name="action" value="train_disease">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-play me-2"></i> Mulai Pelatihan
                    </button>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i> Informasi Model
            </div>
            <div class="card-body">
                <div id="model-info">
                    <div class="model-info">
                        <div class="model-info-item">
                            <span class="model-info-label">Status Model:</span>
                            {% if model_info %}
                                <span id="model-status" class="text-success">Model tersedia</span>
                            {% else %}
                                <span id="model-status" class="text-danger">Model belum tersedia</span>
                            {% endif %}
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Tanggal Pelatihan Terakhir:</span>
                            <span id="model-date">{{ model_info.timestamp if model_info else 'Tidak tersedia' }}</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Akurasi:</span>
                            <span id="model-accuracy">{{ "%.2f"|format(model_info.accuracy) + '%' if model_info and model_info.accuracy else 'Tidak tersedia' }}</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Jumlah Sampel:</span>
                            <span id="model-samples">{{ model_info.n_samples if model_info else 'Tidak tersedia' }}</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Kelas:</span>
                            <span id="model-classes">{{ model_info.classes|join(', ') if model_info and model_info.classes else 'Tidak tersedia' }}</span>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <button class="btn btn-info" id="refresh-info-btn">
                            <i class="fas fa-sync-alt me-2"></i> Refresh Informasi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- End of tab-content -->

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-chart-bar me-2"></i> Visualisasi Model
    </div>
    <div class="card-body text-center">
        <p>Untuk melihat visualisasi model dan metrik evaluasi lengkap, silakan kunjungi halaman <a href="{{ url_for('admin_evaluate') }}">Evaluasi Model</a>.</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateFileName(input) {
    var fileName = input.files[0] ? input.files[0].name : '';
    var fileInfo = document.getElementById('selected-file');
    var uploadBtn = document.getElementById('upload-btn');

    if (fileName) {
        fileInfo.textContent = 'File terpilih: ' + fileName;
        uploadBtn.disabled = false;
    } else {
        fileInfo.textContent = '';
        uploadBtn.disabled = true;
    }
}

// Drag and drop functionality
var uploadArea = document.getElementById('upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#e9ecef';
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';

    var file = e.dataTransfer.files[0];
    var input = document.getElementById('dataset');

    // Create a new FileList object
    var dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    input.files = dataTransfer.files;

    updateFileName(input);
});

// Check if model exists and load model info
function loadModelInfo() {
    console.log('Loading model info...');

    // Set loading state
    try {
        document.getElementById('model-status').textContent = 'Memuat...';
        document.getElementById('model-status').className = 'text-info';
    } catch (e) {
        console.error('Error setting loading state:', e);
    }

    // Simple fetch with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    fetch('/api/model_info', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
        },
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data received:', data);

        if (data && data.success && data.info) {
            console.log('Updating UI with model info...');

            // Update each element individually with error handling
            try {
                const statusEl = document.getElementById('model-status');
                if (statusEl) {
                    statusEl.textContent = 'Model tersedia';
                    statusEl.className = 'text-success';
                }

                const dateEl = document.getElementById('model-date');
                if (dateEl) {
                    dateEl.textContent = data.info.timestamp || 'Tidak tersedia';
                }

                const accuracyEl = document.getElementById('model-accuracy');
                if (accuracyEl) {
                    accuracyEl.textContent = data.info.accuracy ? data.info.accuracy.toFixed(2) + '%' : 'Tidak tersedia';
                }

                const samplesEl = document.getElementById('model-samples');
                if (samplesEl) {
                    samplesEl.textContent = data.info.n_samples || 'Tidak tersedia';
                }

                const classesEl = document.getElementById('model-classes');
                if (classesEl) {
                    classesEl.textContent = data.info.classes ? data.info.classes.join(', ') : 'Tidak tersedia';
                }

                console.log('UI updated successfully');
            } catch (e) {
                console.error('Error updating UI elements:', e);
            }
        } else {
            console.log('Model info failed or invalid data:', data);
            setErrorState('Model belum tersedia');
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        console.error('Error loading model info:', error);
        setErrorState('Error saat memuat informasi model: ' + error.message);
    });
}

function setErrorState(message) {
    try {
        const elements = ['model-status', 'model-date', 'model-accuracy', 'model-samples', 'model-classes'];
        elements.forEach(id => {
            const el = document.getElementById(id);
            if (el) {
                if (id === 'model-status') {
                    el.textContent = message;
                    el.className = 'text-danger';
                } else {
                    el.textContent = 'Tidak tersedia';
                }
            }
        });
    } catch (e) {
        console.error('Error setting error state:', e);
    }
}

// Load model info on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, model info already rendered from server');

    // Setup refresh button untuk update via AJAX
    const refreshBtn = document.getElementById('refresh-info-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            loadModelInfo();
        });
    } else {
        console.error('Refresh button not found');
    }
});
</script>
{% endblock %}
