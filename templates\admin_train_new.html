{% extends "base.html" %}

{% block title %}Latih Model - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
/* Responsive Upload Area */
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-area:hover {
    background-color: #e9ecef;
    border-color: #20c997;
}

.upload-icon {
    font-size: 40px;
    color: #28a745;
    margin-bottom: 10px;
}

.train-icon {
    font-size: 40px;
    color: #007bff;
    margin-bottom: 10px;
}

/* Responsive Model Info */
.model-info {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
}

.model-info-item {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 5px;
}

.model-info-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    flex-shrink: 0;
}

.model-info-value {
    text-align: right;
    word-break: break-word;
    flex: 1;
}

/* Responsive Cards */
.card {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Responsive Buttons */
.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
}

/* Responsive Pre Code Block */
pre {
    font-size: 0.85rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .upload-area {
        padding: 15px;
        min-height: 120px;
    }

    .upload-icon, .train-icon {
        font-size: 30px;
        margin-bottom: 8px;
    }

    .upload-area h5 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .upload-area p {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .model-info {
        padding: 12px;
    }

    .model-info-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 12px;
    }

    .model-info-label {
        margin-bottom: 3px;
        min-width: auto;
    }

    .model-info-value {
        text-align: left;
        font-size: 0.95rem;
    }

    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
        width: 100%;
    }

    .card-body {
        padding: 15px;
    }

    pre {
        font-size: 0.75rem;
        padding: 10px !important;
    }

    h4 {
        font-size: 1.3rem;
    }

    h5 {
        font-size: 1.1rem;
    }
}

/* Tablet Responsive Styles */
@media (max-width: 992px) and (min-width: 769px) {
    .upload-area {
        padding: 18px;
    }

    .upload-icon, .train-icon {
        font-size: 35px;
    }

    .model-info-item {
        margin-bottom: 10px;
    }

    .btn-lg {
        padding: 11px 25px;
        font-size: 1.05rem;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1200px) {
    .upload-area {
        padding: 35px;
        min-height: 180px;
    }

    .upload-icon, .train-icon {
        font-size: 55px;
        margin-bottom: 20px;
    }

    .model-info {
        padding: 25px;
    }

    .model-info-item {
        margin-bottom: 15px;
    }
}

/* Tab Navigation Responsive */
@media (max-width: 576px) {
    .nav-tabs .nav-link {
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    .nav-tabs .nav-link i {
        display: none;
    }
}

/* Utility Classes for Better Spacing */
.mb-responsive {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .mb-responsive {
        margin-bottom: 0.75rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-3 mb-md-4">
                <i class="fas fa-brain me-2 text-primary"></i>
                Latih Model Random Forest
            </h2>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-3 mb-md-4" id="modelTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="disease-tab" data-bs-toggle="tab" href="#disease" role="tab" aria-controls="disease" aria-selected="true">
                        <i class="fas fa-leaf me-1 me-md-2"></i>
                        <span class="d-none d-sm-inline">Model </span>Klasifikasi Penyakit
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="{{ url_for('admin_rice_detector') }}">
                        <i class="fas fa-seedling me-1 me-md-2"></i>
                        <span class="d-none d-sm-inline">Model </span>Deteksi Tanaman Padi
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="modelTabsContent">
        <!-- Disease Classification Tab -->
        <div class="tab-pane fade show active" id="disease" role="tabpanel" aria-labelledby="disease-tab">
            <div class="row g-3 g-md-4">
                <!-- Upload Dataset Section -->
                <div class="col-12 col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <i class="fas fa-upload me-2"></i>
                            <span class="d-none d-sm-inline">Unggah </span>Dataset Penyakit Padi
                        </div>
                        <div class="card-body">
                            <form action="{{ url_for('admin_upload_dataset') }}" method="post" enctype="multipart/form-data">
                                <div class="upload-area" id="upload-area" onclick="document.getElementById('dataset').click();">
                                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                    <h5 class="mb-2">Klik atau seret file dataset ke sini</h5>
                                    <p class="text-muted mb-0">Format: ZIP (Maks. 100MB)</p>
                                    <input type="file" id="dataset" name="dataset" accept=".zip" style="display: none;" onchange="updateFileName(this);">
                                    <p id="selected-file" class="mt-2 mb-0 text-success fw-bold"></p>
                                </div>

                                <div class="text-center mt-3">
                                    <button type="submit" class="btn btn-success btn-lg" id="upload-btn" disabled>
                                        <i class="fas fa-upload me-2"></i>
                                        <span class="d-none d-sm-inline">Unggah </span>Dataset
                                    </button>
                                </div>
                            </form>

                            <!-- Dataset Structure Info -->
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading mb-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Struktur Dataset yang Diharapkan:
                                    </h6>
                                    <div class="d-none d-md-block">
                                        <pre class="bg-light p-3 rounded mb-0">dataset.zip
├── Blast/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Brown_Spot/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Bacterial_Leaf_Blight/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Tungro/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── Sehat/
    ├── image1.jpg
    ├── image2.jpg
    └── ...</pre>
                                    </div>
                                    <div class="d-md-none">
                                        <small>
                                            <strong>Format:</strong> File ZIP dengan folder untuk setiap kelas penyakit:
                                            <br>• Blast/ • Brown_Spot/ • Bacterial_Leaf_Blight/ • Tungro/ • Sehat/
                                            <br>Setiap folder berisi gambar (.jpg, .png)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Train Model Section -->
                <div class="col-12 col-lg-6">
                    <div class="row g-3">
                        <!-- Train Model Card -->
                        <div class="col-12">
                            <div class="card h-100">
                                <div class="card-header">
                                    <i class="fas fa-brain me-2"></i>
                                    <span class="d-none d-sm-inline">Latih </span>Model
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs train-icon"></i>
                                    <h4 class="mb-3">Latih Model Random Forest</h4>
                                    <p class="text-muted mb-4">
                                        Latih model Random Forest dengan dataset yang telah diunggah.
                                        <span class="d-none d-md-inline">Proses pelatihan mungkin memakan waktu beberapa menit tergantung ukuran dataset.</span>
                                    </p>

                                    <form action="{{ url_for('admin_train') }}" method="post">
                                        <input type="hidden" name="action" value="train_disease">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-play me-2"></i>
                                            <span class="d-none d-sm-inline">Mulai </span>Pelatihan
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Model Information Card -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span class="d-none d-sm-inline">Informasi </span>Model
                                </div>
                                <div class="card-body">
                                    <div id="model-info">
                                        <div class="model-info">
                                            <div class="model-info-item">
                                                <span class="model-info-label">Status Model:</span>
                                                <span class="model-info-value">
                                                    {% if model_info %}
                                                        <span id="model-status" class="text-success fw-bold">
                                                            <i class="fas fa-check-circle me-1"></i>Model tersedia
                                                        </span>
                                                    {% else %}
                                                        <span id="model-status" class="text-danger fw-bold">
                                                            <i class="fas fa-times-circle me-1"></i>Model belum tersedia
                                                        </span>
                                                    {% endif %}
                                                </span>
                                            </div>
                                            <div class="model-info-item">
                                                <span class="model-info-label">Tanggal Pelatihan:</span>
                                                <span class="model-info-value" id="model-date">
                                                    {{ model_info.timestamp if model_info else 'Tidak tersedia' }}
                                                </span>
                                            </div>
                                            <div class="model-info-item">
                                                <span class="model-info-label">Akurasi:</span>
                                                <span class="model-info-value" id="model-accuracy">
                                                    {% if model_info and model_info.accuracy %}
                                                        <span class="badge bg-success fs-6">{{ "%.2f"|format(model_info.accuracy) }}%</span>
                                                    {% else %}
                                                        Tidak tersedia
                                                    {% endif %}
                                                </span>
                                            </div>
                                            <div class="model-info-item">
                                                <span class="model-info-label">Jumlah Sampel:</span>
                                                <span class="model-info-value" id="model-samples">
                                                    {% if model_info and model_info.n_samples %}
                                                        <span class="badge bg-info fs-6">{{ model_info.n_samples }}</span>
                                                    {% else %}
                                                        Tidak tersedia
                                                    {% endif %}
                                                </span>
                                            </div>
                                            <div class="model-info-item">
                                                <span class="model-info-label">Kelas:</span>
                                                <span class="model-info-value" id="model-classes">
                                                    {% if model_info and model_info.classes %}
                                                        <div class="d-flex flex-wrap gap-1">
                                                            {% for class in model_info.classes %}
                                                                <span class="badge bg-secondary">{{ class }}</span>
                                                            {% endfor %}
                                                        </div>
                                                    {% else %}
                                                        Tidak tersedia
                                                    {% endif %}
                                                </span>
                                            </div>
                                        </div>

                                        <div class="text-center mt-3">
                                            <button class="btn btn-outline-info" id="refresh-info-btn">
                                                <i class="fas fa-sync-alt me-2"></i>
                                                <span class="d-none d-sm-inline">Refresh </span>Informasi
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- End of tab-content -->

        <!-- Visualization Card -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>
                        <span class="d-none d-sm-inline">Visualisasi </span>Model
                    </div>
                    <div class="card-body text-center">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-8">
                                <p class="mb-2 mb-md-0">
                                    Untuk melihat visualisasi model dan metrik evaluasi lengkap,
                                    silakan kunjungi halaman evaluasi model.
                                </p>
                            </div>
                            <div class="col-12 col-md-4">
                                <a href="{{ url_for('admin_evaluate') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-chart-line me-2"></i>
                                    <span class="d-none d-sm-inline">Lihat </span>Evaluasi Model
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateFileName(input) {
    var fileName = input.files[0] ? input.files[0].name : '';
    var fileInfo = document.getElementById('selected-file');
    var uploadBtn = document.getElementById('upload-btn');

    if (fileName) {
        fileInfo.textContent = 'File terpilih: ' + fileName;
        uploadBtn.disabled = false;
    } else {
        fileInfo.textContent = '';
        uploadBtn.disabled = true;
    }
}

// Drag and drop functionality
var uploadArea = document.getElementById('upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#e9ecef';
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';

    var file = e.dataTransfer.files[0];
    var input = document.getElementById('dataset');

    // Create a new FileList object
    var dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    input.files = dataTransfer.files;

    updateFileName(input);
});

// Check if model exists and load model info
function loadModelInfo() {
    console.log('Loading model info...');

    // Set loading state
    try {
        document.getElementById('model-status').textContent = 'Memuat...';
        document.getElementById('model-status').className = 'text-info';
    } catch (e) {
        console.error('Error setting loading state:', e);
    }

    // Simple fetch with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    fetch('/api/model_info', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
        },
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data received:', data);

        if (data && data.success && data.info) {
            console.log('Updating UI with model info...');

            // Update each element individually with error handling
            try {
                const statusEl = document.getElementById('model-status');
                if (statusEl) {
                    statusEl.textContent = 'Model tersedia';
                    statusEl.className = 'text-success';
                }

                const dateEl = document.getElementById('model-date');
                if (dateEl) {
                    dateEl.textContent = data.info.timestamp || 'Tidak tersedia';
                }

                const accuracyEl = document.getElementById('model-accuracy');
                if (accuracyEl) {
                    accuracyEl.textContent = data.info.accuracy ? data.info.accuracy.toFixed(2) + '%' : 'Tidak tersedia';
                }

                const samplesEl = document.getElementById('model-samples');
                if (samplesEl) {
                    samplesEl.textContent = data.info.n_samples || 'Tidak tersedia';
                }

                const classesEl = document.getElementById('model-classes');
                if (classesEl) {
                    classesEl.textContent = data.info.classes ? data.info.classes.join(', ') : 'Tidak tersedia';
                }

                console.log('UI updated successfully');
            } catch (e) {
                console.error('Error updating UI elements:', e);
            }
        } else {
            console.log('Model info failed or invalid data:', data);
            setErrorState('Model belum tersedia');
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        console.error('Error loading model info:', error);
        setErrorState('Error saat memuat informasi model: ' + error.message);
    });
}

function setErrorState(message) {
    try {
        const elements = ['model-status', 'model-date', 'model-accuracy', 'model-samples', 'model-classes'];
        elements.forEach(id => {
            const el = document.getElementById(id);
            if (el) {
                if (id === 'model-status') {
                    el.textContent = message;
                    el.className = 'text-danger';
                } else {
                    el.textContent = 'Tidak tersedia';
                }
            }
        });
    } catch (e) {
        console.error('Error setting error state:', e);
    }
}

// Load model info on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, model info already rendered from server');

    // Setup refresh button untuk update via AJAX
    const refreshBtn = document.getElementById('refresh-info-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            loadModelInfo();
        });
    } else {
        console.error('Refresh button not found');
    }
});
</script>
{% endblock %}
