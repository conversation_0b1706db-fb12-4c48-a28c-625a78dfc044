{% extends "base.html" %}

{% block title %}Latih Model - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}
.upload-area:hover {
    background-color: #e9ecef;
}
.upload-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}
.train-icon {
    font-size: 50px;
    color: #007bff;
    margin-bottom: 15px;
}
.model-info {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}
.model-info-item {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}
.model-info-label {
    font-weight: 600;
}
{% endblock %}

{% block content %}
<h2 class="mb-4">Latih Model Random Forest</h2>

<!-- Tab Navigation -->
<ul class="nav nav-tabs mb-4" id="modelTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="disease-tab" data-bs-toggle="tab" data-bs-target="#disease" type="button" role="tab" aria-controls="disease" aria-selected="true">
            <i class="fas fa-leaf me-2"></i>Model Klasifikasi Penyakit
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="rice-detector-tab" data-bs-toggle="tab" data-bs-target="#rice-detector" type="button" role="tab" aria-controls="rice-detector" aria-selected="false">
            <i class="fas fa-seedling me-2"></i>Model Deteksi Tanaman Padi
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="modelTabsContent">
    <!-- Disease Classification Tab -->
    <div class="tab-pane fade show active" id="disease" role="tabpanel" aria-labelledby="disease-tab">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-upload me-2"></i> Unggah Dataset Penyakit Padi
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('admin_upload_dataset') }}" method="post" enctype="multipart/form-data">
                            <div class="upload-area" id="upload-area" onclick="document.getElementById('dataset').click();">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5>Klik atau seret file dataset ke sini</h5>
                                <p class="text-muted">Format yang didukung: ZIP (Maks. 100MB)</p>
                                <input type="file" id="dataset" name="dataset" accept=".zip" style="display: none;" onchange="updateFileName(this);">
                                <p id="selected-file" class="mt-3"></p>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg" id="upload-btn" disabled>
                                    <i class="fas fa-upload me-2"></i> Unggah Dataset
                                </button>
                            </div>
                        </form>

                <div class="mt-4">
                    <h5>Struktur Dataset yang Diharapkan:</h5>
                    <pre class="bg-light p-3 rounded">
dataset.zip
├── Blast/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Brown_Spot/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Bacterial_Leaf_Blight/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Tungro/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── Sehat/
    ├── image1.jpg
    ├── image2.jpg
    └── ...
                    </pre>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-brain me-2"></i> Latih Model
            </div>
            <div class="card-body text-center">
                <i class="fas fa-cogs train-icon"></i>
                <h4 class="mb-3">Latih Model Random Forest</h4>
                <p>Latih model Random Forest dengan dataset yang telah diunggah. Proses pelatihan mungkin memakan waktu beberapa menit tergantung ukuran dataset.</p>

                <form action="{{ url_for('admin_train') }}" method="post">
                    <input type="hidden" name="action" value="train_disease">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-play me-2"></i> Mulai Pelatihan
                    </button>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i> Informasi Model
            </div>
            <div class="card-body">
                <div id="model-info">
                    <div class="model-info">
                        <div class="model-info-item">
                            <span class="model-info-label">Status Model:</span>
                            <span id="model-status">Memuat...</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Tanggal Pelatihan Terakhir:</span>
                            <span id="model-date">Memuat...</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Akurasi:</span>
                            <span id="model-accuracy">Memuat...</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Jumlah Sampel:</span>
                            <span id="model-samples">Memuat...</span>
                        </div>
                        <div class="model-info-item">
                            <span class="model-info-label">Kelas:</span>
                            <span id="model-classes">Memuat...</span>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <button class="btn btn-info" id="refresh-info-btn">
                            <i class="fas fa-sync-alt me-2"></i> Refresh Informasi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rice Detection Tab -->
    <div class="tab-pane fade" id="rice-detector" role="tabpanel" aria-labelledby="rice-detector-tab">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-upload me-2"></i> Unggah Dataset Non-Padi
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('admin_upload_non_rice_dataset') }}" method="post" enctype="multipart/form-data">
                            <div class="upload-area" id="upload-area-non-rice" onclick="document.getElementById('non_rice_dataset').click();">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5>Klik atau seret file dataset non-padi ke sini</h5>
                                <p class="text-muted">Format yang didukung: ZIP (Maks. 100MB)</p>
                                <input type="file" id="non_rice_dataset" name="non_rice_dataset" accept=".zip" style="display: none;" onchange="updateFileNameNonRice(this);">
                                <p id="selected-file-non-rice" class="mt-3"></p>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg" id="upload-btn-non-rice" disabled>
                                    <i class="fas fa-upload me-2"></i> Unggah Dataset Non-Padi
                                </button>
                            </div>
                        </form>

                        <div class="mt-4">
                            <h5>Dataset Non-Padi:</h5>
                            <p class="text-muted">Dataset ini berisi gambar-gambar yang bukan tanaman padi, seperti:</p>
                            <ul class="text-muted">
                                <li>Tanaman lain (jagung, kedelai, dll)</li>
                                <li>Objek non-tanaman (bangunan, kendaraan, dll)</li>
                                <li>Pemandangan umum</li>
                                <li>Hewan</li>
                            </ul>
                            <pre class="bg-light p-3 rounded">
non_rice_dataset.zip
├── corn/
│   ├── image1.jpg
│   └── ...
├── buildings/
│   ├── image1.jpg
│   └── ...
├── animals/
│   ├── image1.jpg
│   └── ...
└── others/
    ├── image1.jpg
    └── ...
                            </pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-brain me-2"></i> Latih Model Deteksi Padi
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-seedling train-icon"></i>
                        <h4 class="mb-3">Latih Model Deteksi Tanaman Padi</h4>
                        <p>Model ini akan membedakan antara gambar tanaman padi dan gambar lainnya. Pastikan dataset penyakit padi dan dataset non-padi sudah diunggah.</p>

                        <form action="{{ url_for('admin_train') }}" method="post">
                            <input type="hidden" name="action" value="train_rice_detector">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-play me-2"></i> Latih Model Deteksi
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-2"></i> Informasi Model Deteksi
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Catatan:</strong> Model deteksi tanaman padi akan digunakan untuk memfilter gambar sebelum klasifikasi penyakit. Hanya gambar yang terdeteksi sebagai tanaman padi yang akan diklasifikasi penyakitnya.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-chart-bar me-2"></i> Visualisasi Model
    </div>
    <div class="card-body text-center">
        <p>Untuk melihat visualisasi model dan metrik evaluasi lengkap, silakan kunjungi halaman <a href="{{ url_for('admin_evaluate') }}">Evaluasi Model</a>.</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
function updateFileName(input) {
    var fileName = input.files[0] ? input.files[0].name : '';
    var fileInfo = document.getElementById('selected-file');
    var uploadBtn = document.getElementById('upload-btn');

    if (fileName) {
        fileInfo.textContent = 'File terpilih: ' + fileName;
        uploadBtn.disabled = false;
    } else {
        fileInfo.textContent = '';
        uploadBtn.disabled = true;
    }
}

function updateFileNameNonRice(input) {
    var fileName = input.files[0] ? input.files[0].name : '';
    var fileInfo = document.getElementById('selected-file-non-rice');
    var uploadBtn = document.getElementById('upload-btn-non-rice');

    if (fileName) {
        fileInfo.textContent = 'File terpilih: ' + fileName;
        uploadBtn.disabled = false;
    } else {
        fileInfo.textContent = '';
        uploadBtn.disabled = true;
    }
}

// Drag and drop functionality
var uploadArea = document.getElementById('upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#e9ecef';
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';

    var file = e.dataTransfer.files[0];
    var input = document.getElementById('dataset');

    // Create a new FileList object
    var dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    input.files = dataTransfer.files;

    updateFileName(input);
});

// Drag and drop functionality for non-rice dataset
var uploadAreaNonRice = document.getElementById('upload-area-non-rice');

if (uploadAreaNonRice) {
    uploadAreaNonRice.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#e9ecef';
    });

    uploadAreaNonRice.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#f8f9fa';
    });

    uploadAreaNonRice.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#f8f9fa';

        var file = e.dataTransfer.files[0];
        var input = document.getElementById('non_rice_dataset');

        // Create a new FileList object
        var dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        input.files = dataTransfer.files;

        updateFileNameNonRice(input);
    });
}

// Check if model exists and load model info
function loadModelInfo() {
    fetch('/api/model_info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('model-status').textContent = 'Model tersedia';
                document.getElementById('model-status').className = 'text-success';
                document.getElementById('model-date').textContent = data.info.timestamp || 'Tidak tersedia';
                document.getElementById('model-accuracy').textContent = data.info.accuracy ? data.info.accuracy.toFixed(2) + '%' : 'Tidak tersedia';
                document.getElementById('model-samples').textContent = data.info.n_samples || 'Tidak tersedia';
                document.getElementById('model-classes').textContent = data.info.classes ? data.info.classes.join(', ') : 'Tidak tersedia';
            } else {
                document.getElementById('model-status').textContent = 'Model belum tersedia';
                document.getElementById('model-status').className = 'text-danger';
                document.getElementById('model-date').textContent = 'Tidak tersedia';
                document.getElementById('model-accuracy').textContent = 'Tidak tersedia';
                document.getElementById('model-samples').textContent = 'Tidak tersedia';
                document.getElementById('model-classes').textContent = 'Tidak tersedia';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('model-status').textContent = 'Error saat memuat informasi model';
            document.getElementById('model-status').className = 'text-danger';
        });
}

// Load model info on page load
document.addEventListener('DOMContentLoaded', function() {
    loadModelInfo();

    // Initialize Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#modelTabs button'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl)

        triggerEl.addEventListener('click', function (event) {
            event.preventDefault()
            tabTrigger.show()
        })
    })
});

// Refresh model info button
document.getElementById('refresh-info-btn').addEventListener('click', loadModelInfo);
{% endblock %}
