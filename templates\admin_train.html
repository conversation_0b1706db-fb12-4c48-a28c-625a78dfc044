<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Latih Model - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .train-icon {
            font-size: 60px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .upload-area {
            border: 2px dashed #28a745;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            background-color: #e9ecef;
        }
        .upload-icon {
            font-size: 50px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .model-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .model-info-item {
            margin-bottom: 10px;
        }
        .model-info-label {
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}" class="active">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            <a href="{{ url_for('admin_evaluate') }}">
                <i class="fas fa-chart-bar"></i> Evaluasi Model
            </a>
            <a href="{{ url_for('admin_diseases') }}">
                <i class="fas fa-virus"></i> Kelola Penyakit
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>

        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <h2 class="mb-4">Latih Model Random Forest</h2>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-upload me-2"></i> Unggah Dataset
                        </div>
                        <div class="card-body">
                            <form action="{{ url_for('admin_upload_dataset') }}" method="post" enctype="multipart/form-data">
                                <div class="upload-area" id="upload-area" onclick="document.getElementById('dataset').click();">
                                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                    <h5>Klik atau seret file dataset ke sini</h5>
                                    <p class="text-muted">Format yang didukung: ZIP (Maks. 100MB)</p>
                                    <input type="file" id="dataset" name="dataset" accept=".zip" style="display: none;" onchange="updateFileName(this);">
                                    <p id="selected-file" class="mt-3"></p>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-success btn-lg" id="upload-btn" disabled>
                                        <i class="fas fa-upload me-2"></i> Unggah Dataset
                                    </button>
                                </div>
                            </form>

                            <div class="mt-4">
                                <h5>Struktur Dataset yang Diharapkan:</h5>
                                <pre class="bg-light p-3 rounded">
dataset.zip
├── Blast/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Brown_Spot/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Bacterial_Leaf_Blight/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Tungro/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── Sehat/
    ├── image1.jpg
    ├── image2.jpg
    └── ...
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-brain me-2"></i> Latih Model
                        </div>
                        <div class="card-body text-center">
                            <i class="fas fa-cogs train-icon"></i>
                            <h4 class="mb-3">Latih Model Random Forest</h4>
                            <p>Latih model Random Forest dengan dataset yang telah diunggah. Proses pelatihan mungkin memakan waktu beberapa menit tergantung ukuran dataset.</p>

                            <form action="{{ url_for('admin_train') }}" method="post">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-play me-2"></i> Mulai Pelatihan
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <i class="fas fa-info-circle me-2"></i> Informasi Model
                        </div>
                        <div class="card-body">
                            <div id="model-info">
                                <div class="model-info">
                                    <div class="model-info-item">
                                        <span class="model-info-label">Status Model:</span>
                                        <span id="model-status">Memuat...</span>
                                    </div>
                                    <div class="model-info-item">
                                        <span class="model-info-label">Tanggal Pelatihan Terakhir:</span>
                                        <span id="model-date">Memuat...</span>
                                    </div>
                                    <div class="model-info-item">
                                        <span class="model-info-label">Akurasi:</span>
                                        <span id="model-accuracy">Memuat...</span>
                                    </div>
                                    <div class="model-info-item">
                                        <span class="model-info-label">Jumlah Sampel:</span>
                                        <span id="model-samples">Memuat...</span>
                                    </div>
                                    <div class="model-info-item">
                                        <span class="model-info-label">Kelas:</span>
                                        <span id="model-classes">Memuat...</span>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <button class="btn btn-info" id="refresh-info-btn">
                                        <i class="fas fa-sync-alt me-2"></i> Refresh Informasi
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i> Visualisasi Model
                </div>
                <div class="card-body text-center">
                    <p>Untuk melihat visualisasi model dan metrik evaluasi lengkap, silakan kunjungi halaman <a href="{{ url_for('admin_evaluate') }}">Evaluasi Model</a>.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function updateFileName(input) {
            var fileName = input.files[0] ? input.files[0].name : '';
            var fileInfo = document.getElementById('selected-file');
            var uploadBtn = document.getElementById('upload-btn');

            if (fileName) {
                fileInfo.textContent = 'File terpilih: ' + fileName;
                uploadBtn.disabled = false;
            } else {
                fileInfo.textContent = '';
                uploadBtn.disabled = true;
            }
        }

        // Drag and drop functionality
        var uploadArea = document.getElementById('upload-area');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#e9ecef';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';

            var file = e.dataTransfer.files[0];
            var input = document.getElementById('dataset');

            // Create a new FileList object
            var dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            input.files = dataTransfer.files;

            updateFileName(input);
        });

        // Check if model exists and load model info
        function loadModelInfo() {
            fetch('/api/model_info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('model-status').textContent = 'Model tersedia';
                        document.getElementById('model-status').className = 'text-success';
                        document.getElementById('model-date').textContent = data.info.timestamp || 'Tidak tersedia';
                        document.getElementById('model-accuracy').textContent = data.info.accuracy ? data.info.accuracy.toFixed(2) + '%' : 'Tidak tersedia';
                        document.getElementById('model-samples').textContent = data.info.n_samples || 'Tidak tersedia';
                        document.getElementById('model-classes').textContent = data.info.classes ? data.info.classes.join(', ') : 'Tidak tersedia';
                    } else {
                        document.getElementById('model-status').textContent = 'Model belum tersedia';
                        document.getElementById('model-status').className = 'text-danger';
                        document.getElementById('model-date').textContent = 'Tidak tersedia';
                        document.getElementById('model-accuracy').textContent = 'Tidak tersedia';
                        document.getElementById('model-samples').textContent = 'Tidak tersedia';
                        document.getElementById('model-classes').textContent = 'Tidak tersedia';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('model-status').textContent = 'Error saat memuat informasi model';
                    document.getElementById('model-status').className = 'text-danger';
                });
        }

        // Load model info on page load
        document.addEventListener('DOMContentLoaded', loadModelInfo);

        // Refresh model info button
        document.getElementById('refresh-info-btn').addEventListener('click', loadModelInfo);
    </script>
</body>
</html>
