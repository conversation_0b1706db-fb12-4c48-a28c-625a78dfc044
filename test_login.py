import sqlite3
import os
from werkzeug.security import check_password_hash, generate_password_hash

# Pastikan database ada
db_path = 'database/padiku.db'
if not os.path.exists(db_path):
    print(f"Database tidak ditemukan di {db_path}")
    exit(1)

# Buat koneksi ke database
conn = sqlite3.connect(db_path)
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Fungsi untuk menguji login
def test_login(username, password):
    print(f"Testing login: username={username}, password={'*' * len(password)}")
    
    # Cari pengguna
    cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
    user = cursor.fetchone()
    
    if user:
        print(f"User found: id={user['id']}, username={user['username']}, role={user['role']}")
        print(f"Password hash: {user['password']}")
        
        # Cek password
        is_valid = check_password_hash(user['password'], password)
        print(f"Password valid: {is_valid}")
        
        if not is_valid:
            # Buat hash baru untuk password yang sama
            new_hash = generate_password_hash(password)
            print(f"New hash for same password: {new_hash}")
            print(f"Would this new hash validate? {check_password_hash(new_hash, password)}")
            
            # Update password di database
            print("Updating password in database...")
            cursor.execute('UPDATE users SET password = ? WHERE id = ?', (new_hash, user['id']))
            conn.commit()
            print("Password updated")
    else:
        print(f"User not found: {username}")

try:
    # Uji login untuk admin
    test_login('admin', 'admin123')
    print("\n")
    
    # Uji login untuk user
    test_login('user', 'user123')
    print("\n")
    
    # Uji login untuk admin2
    test_login('admin2', 'admin123')
    
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    conn.close()
