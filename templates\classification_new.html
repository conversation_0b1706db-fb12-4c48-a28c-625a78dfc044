{% extends "base.html" %}

{% block title %}Klasifikasi Penyakit - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-area:hover {
    background-color: #e9ecef;
    border-color: #20c997;
}

.upload-icon {
    font-size: 45px;
    color: #28a745;
    margin-bottom: 12px;
}

#preview-image {
    max-width: 100%;
    max-height: 250px;
    margin-top: 15px;
    border-radius: 8px;
    display: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .upload-area {
        padding: 20px 15px;
        min-height: 150px;
    }

    .upload-icon {
        font-size: 35px;
        margin-bottom: 10px;
    }

    .upload-area h5 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .upload-area p {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    #preview-image {
        max-height: 200px;
        margin-top: 12px;
    }

    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
        width: 100%;
    }

    .card-body {
        padding: 15px;
    }

    h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem !important;
    }
}

@media (max-width: 576px) {
    .upload-area {
        padding: 15px 10px;
        min-height: 130px;
    }

    .upload-icon {
        font-size: 30px;
        margin-bottom: 8px;
    }

    .upload-area h5 {
        font-size: 0.95rem;
    }

    .upload-area p {
        font-size: 0.85rem;
    }

    #preview-image {
        max-height: 180px;
    }

    .btn-lg {
        padding: 8px 16px;
        font-size: 0.95rem;
    }

    .card-body {
        padding: 12px;
    }

    .card-body ol {
        padding-left: 20px;
    }

    .card-body ol li {
        margin-bottom: 8px;
        font-size: 0.9rem;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1200px) {
    .upload-area {
        padding: 40px;
        min-height: 250px;
    }

    .upload-icon {
        font-size: 60px;
        margin-bottom: 20px;
    }

    #preview-image {
        max-height: 350px;
        margin-top: 20px;
    }
}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-3 mb-md-4">
                <i class="fas fa-microscope me-2 text-primary"></i>
                <span class="d-none d-sm-inline">Klasifikasi Penyakit </span>Tanaman Padi
            </h2>
        </div>
    </div>

    <div class="row g-3 g-md-4">
        <!-- Upload Section -->
        <div class="col-12 col-lg-8">
            <div class="card h-100">
                <div class="card-header">
                    <i class="fas fa-upload me-2"></i>
                    <span class="d-none d-sm-inline">Unggah Gambar </span>Tanaman Padi
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div class="upload-area" id="upload-area" onclick="document.getElementById('image').click();">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h5 class="mb-2">Klik atau seret gambar ke sini</h5>
                            <p class="text-muted mb-0">Format: JPG, JPEG, PNG (Maks. 16MB)</p>
                            <input type="file" id="image" name="image" accept="image/*" style="display: none;" onchange="previewImage(this);">
                            <img id="preview-image" src="#" alt="Preview">
                        </div>

                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-success btn-lg" id="classify-btn" disabled>
                                <i class="fas fa-microscope me-2"></i>
                                <span class="d-none d-sm-inline">Klasifikasi </span>Penyakit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Instructions Section -->
        <div class="col-12 col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i>
                    <span class="d-none d-sm-inline">Petunjuk </span>Penggunaan
                </div>
                <div class="card-body">
                    <div class="d-none d-md-block">
                        <ol class="mb-0">
                            <li class="mb-2">Unggah gambar daun tanaman padi yang ingin diklasifikasi.</li>
                            <li class="mb-2">Pastikan gambar memiliki pencahayaan yang baik dan fokus pada bagian yang terinfeksi.</li>
                            <li class="mb-2">Klik tombol "Klasifikasi Penyakit" untuk memulai proses klasifikasi.</li>
                            <li class="mb-2">Sistem akan menampilkan hasil klasifikasi beserta tingkat kepercayaan.</li>
                            <li class="mb-0">Hasil klasifikasi akan disimpan dalam riwayat dan dapat dilihat di halaman profil.</li>
                        </ol>
                    </div>
                    <div class="d-md-none">
                        <div class="alert alert-info mb-0">
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-lightbulb me-1"></i>Tips:
                            </h6>
                            <ul class="mb-0 ps-3">
                                <li>Gunakan gambar yang jelas dan terang</li>
                                <li>Fokus pada area yang terinfeksi</li>
                                <li>Format: JPG, PNG (max 16MB)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
function previewImage(input) {
    var preview = document.getElementById('preview-image');
    var classifyBtn = document.getElementById('classify-btn');
    
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            classifyBtn.disabled = false;
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
        classifyBtn.disabled = true;
    }
}

// Drag and drop functionality
var uploadArea = document.getElementById('upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#e9ecef';
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
    
    var file = e.dataTransfer.files[0];
    var input = document.getElementById('image');
    
    // Create a new FileList object
    var dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    input.files = dataTransfer.files;
    
    previewImage(input);
});
{% endblock %}
