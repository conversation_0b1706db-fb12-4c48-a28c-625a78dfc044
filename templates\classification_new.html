{% extends "base.html" %}

{% block title %}Klasifikasi Penyakit - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.upload-area {
    border: 2px dashed #28a745;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}
.upload-area:hover {
    background-color: #e9ecef;
}
.upload-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}
#preview-image {
    max-width: 100%;
    max-height: 300px;
    margin-top: 20px;
    border-radius: 5px;
    display: none;
}
{% endblock %}

{% block content %}
<h2 class="mb-4">Klasifikasi Penyakit Tanaman Padi</h2>

<div class="card">
    <div class="card-header">
        <i class="fas fa-upload me-2"></i> Unggah Gambar Tanaman Padi
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            <div class="upload-area" id="upload-area" onclick="document.getElementById('image').click();">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <h5>Klik atau seret gambar ke sini</h5>
                <p class="text-muted">Format yang didukung: JPG, JPEG, PNG (Maks. 16MB)</p>
                <input type="file" id="image" name="image" accept="image/*" style="display: none;" onchange="previewImage(this);">
                <img id="preview-image" src="#" alt="Preview">
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-success btn-lg" id="classify-btn" disabled>
                    <i class="fas fa-microscope me-2"></i> Klasifikasi Penyakit
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-info-circle me-2"></i> Petunjuk Penggunaan
    </div>
    <div class="card-body">
        <ol>
            <li>Unggah gambar daun tanaman padi yang ingin diklasifikasi.</li>
            <li>Pastikan gambar memiliki pencahayaan yang baik dan fokus pada bagian yang terinfeksi.</li>
            <li>Klik tombol "Klasifikasi Penyakit" untuk memulai proses klasifikasi.</li>
            <li>Sistem akan menampilkan hasil klasifikasi beserta tingkat kepercayaan.</li>
            <li>Hasil klasifikasi akan disimpan dalam riwayat dan dapat dilihat di halaman profil.</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
function previewImage(input) {
    var preview = document.getElementById('preview-image');
    var classifyBtn = document.getElementById('classify-btn');
    
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            classifyBtn.disabled = false;
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
        classifyBtn.disabled = true;
    }
}

// Drag and drop functionality
var uploadArea = document.getElementById('upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#e9ecef';
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f8f9fa';
    
    var file = e.dataTransfer.files[0];
    var input = document.getElementById('image');
    
    // Create a new FileList object
    var dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    input.files = dataTransfer.files;
    
    previewImage(input);
});
{% endblock %}
