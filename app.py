import os
import sqlite3
import json
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import numpy as np
import cv2
import joblib
from datetime import datetime

# Inisialisasi aplikasi Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'padiku-secret-key'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MODEL_FOLDER'] = 'static/models'
app.config['DATABASE'] = 'database/padiku.db'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Pastikan direktori upload ada
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['MODEL_FOLDER'], exist_ok=True)

# Inisialisasi Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Kelas User untuk autentikasi
class User(UserMixin):
    def __init__(self, id, username, email, role, full_name=None, address=None):
        self.id = id
        self.username = username
        self.email = email
        self.role = role
        self.full_name = full_name
        self.address = address

# Fungsi untuk mendapatkan koneksi database
def get_db():
    conn = sqlite3.connect(app.config['DATABASE'])
    conn.row_factory = sqlite3.Row
    return conn

# Fungsi helper untuk mengakses kolom sqlite3.Row dengan aman
def safe_get(row, column, default=None):
    try:
        return row[column]
    except (IndexError, KeyError):
        return default

# Fungsi untuk inisialisasi database
def init_db():
    with app.app_context():
        db = get_db()
        with app.open_resource('database/schema.sql', mode='r') as f:
            db.cursor().executescript(f.read())
        db.commit()

# Fungsi untuk migrasi database
def migrate_db():
    print("Memulai migrasi database...")
    db = get_db()
    cursor = db.cursor()

    try:
        # Cek apakah kolom full_name dan address sudah ada di tabel users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        print(f"Kolom yang ada di tabel users: {column_names}")

        # Tambahkan kolom full_name jika belum ada
        if 'full_name' not in column_names:
            print("Menambahkan kolom full_name ke tabel users...")
            cursor.execute("ALTER TABLE users ADD COLUMN full_name TEXT DEFAULT NULL")

        # Tambahkan kolom address jika belum ada
        if 'address' not in column_names:
            print("Menambahkan kolom address ke tabel users...")
            cursor.execute("ALTER TABLE users ADD COLUMN address TEXT DEFAULT NULL")

        # Commit perubahan
        db.commit()
        print("Migrasi database selesai!")
    except Exception as e:
        print(f"Error saat migrasi database: {str(e)}")
        db.rollback()

# Fungsi untuk mendapatkan user berdasarkan ID
@login_manager.user_loader
def load_user(user_id):
    db = get_db()
    user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if user:
        return User(
            user['id'],
            user['username'],
            user['email'],
            user['role'],
            safe_get(user, 'full_name'),
            safe_get(user, 'address')
        )
    return None

# Fungsi untuk preprocessing gambar
def preprocess_image(image_path, target_size=(224, 224)):
    # Gunakan fungsi ekstraksi fitur dari model.py untuk konsistensi
    from models.model import extract_features
    features = extract_features(image_path, target_size)
    return features

# Fungsi untuk prediksi penyakit
def predict_disease(image_path):
    try:
        # Pertama, periksa apakah gambar adalah tanaman padi
        from models.model import is_rice_plant

        print(f"Memeriksa apakah gambar adalah tanaman padi: {image_path}")
        if not is_rice_plant(image_path):
            return {
                "error": "not_rice_plant",
                "message": "Gambar yang diunggah bukan tanaman padi. Sistem ini khusus untuk klasifikasi penyakit tanaman padi."
            }

        # Load model klasifikasi penyakit
        model_path = os.path.join(app.config['MODEL_FOLDER'], 'random_forest_model.pkl')
        if not os.path.exists(model_path):
            return {"error": "Model belum dilatih"}

        model = joblib.load(model_path)

        # Preprocess gambar
        features = preprocess_image(image_path)

        # Prediksi
        prediction = model.predict([features])[0]
        probabilities = model.predict_proba([features])[0]

        # Dapatkan nama kelas
        class_names = model.classes_

        # Buat hasil
        result = {
            "prediction": prediction,
            "confidence": float(max(probabilities) * 100),
            "probabilities": {class_names[i]: float(prob * 100) for i, prob in enumerate(probabilities)},
            "is_rice_plant": True
        }

        return result
    except Exception as e:
        return {"error": str(e)}

# Routes
@app.route('/')
def index():
    return redirect(url_for('login'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        address = request.form.get('address')
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = 'user'  # Selalu tetapkan sebagai 'user'
        terms = request.form.get('terms')

        # Validasi input
        if not full_name or not email or not address or not username or not password or not confirm_password:
            flash('Semua field harus diisi', 'danger')
            return redirect(url_for('register'))

        if password != confirm_password:
            flash('Password dan konfirmasi password tidak cocok', 'danger')
            return redirect(url_for('register'))

        if not terms:
            flash('Anda harus menyetujui syarat dan ketentuan', 'danger')
            return redirect(url_for('register'))

        # Role selalu 'user', tidak perlu validasi

        # Hash password
        hashed_password = generate_password_hash(password)

        # Simpan user baru ke database
        db = get_db()
        try:
            db.execute('''
                INSERT INTO users (username, email, password, full_name, address, role)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, email, hashed_password, full_name, address, role))
            db.commit()
            flash('Registrasi berhasil! Silakan login.', 'success')
            return redirect(url_for('login'))
        except sqlite3.IntegrityError:
            flash('Username atau email sudah digunakan', 'danger')
            return redirect(url_for('register'))

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        role = request.form.get('role', 'user')  # Default ke 'user' jika tidak ada

        # Debug info
        print(f"Login attempt: username={username}, password={'*' * len(password)}, role={role}")

        db = get_db()

        # Jika role dipilih, cari user dengan role tersebut
        if role in ['admin', 'user']:
            user = db.execute('SELECT * FROM users WHERE username = ? AND role = ?', (username, role)).fetchone()
            if not user:
                # Jika tidak ditemukan dengan role yang dipilih, coba cari tanpa memperhatikan role
                user = db.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
                if user:
                    print(f"User found but with different role: {user['role']}, requested: {role}")
        else:
            # Jika role tidak valid, cari user tanpa memperhatikan role
            user = db.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

        if user:
            print(f"User found: id={user['id']}, username={user['username']}, role={user['role']}")
            print(f"Password hash: {user['password'][:30]}...")

            is_valid = check_password_hash(user['password'], password)
            print(f"Password valid: {is_valid}")

            if is_valid:
                # Periksa apakah role sesuai dengan yang diminta
                if role != user['role'] and role in ['admin', 'user']:
                    flash(f"Login gagal. Anda mencoba login sebagai {role}, tetapi akun Anda adalah {user['role']}.", 'danger')
                    return redirect(url_for('login'))

                user_obj = User(
                    user['id'],
                    user['username'],
                    user['email'],
                    user['role'],
                    safe_get(user, 'full_name'),
                    safe_get(user, 'address')
                )
                login_user(user_obj)
                flash('Login berhasil!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('Login gagal. Password tidak valid.', 'danger')
        else:
            print(f"User not found: {username}")
            flash('Login gagal. Username tidak ditemukan.', 'danger')

    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    # Ambil statistik untuk dashboard
    db = get_db()
    total_users = db.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']
    total_classifications = db.execute('SELECT COUNT(*) as count FROM classifications').fetchone()['count']

    recent_classifications = db.execute('''
        SELECT c.*, u.username
        FROM classifications c
        JOIN users u ON c.user_id = u.id
        ORDER BY c.created_at DESC LIMIT 5
    ''').fetchall()

    return render_template('dashboard_new.html',
                          total_users=total_users,
                          total_classifications=total_classifications,
                          recent_classifications=recent_classifications)

@app.route('/classification', methods=['GET', 'POST'])
@login_required
def classification():
    if request.method == 'POST':
        # Cek apakah ada file yang diunggah
        if 'image' not in request.files:
            flash('Tidak ada file yang dipilih', 'danger')
            return redirect(request.url)

        file = request.files['image']

        if file.filename == '':
            flash('Tidak ada file yang dipilih', 'danger')
            return redirect(request.url)

        if file:
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Lakukan prediksi
            result = predict_disease(filepath)

            if "error" in result:
                if result["error"] == "not_rice_plant":
                    flash(result["message"], 'warning')
                else:
                    flash(f"Error: {result['error']}", 'danger')
                return redirect(request.url)

            # Simpan hasil klasifikasi ke database
            db = get_db()
            db.execute('''
                INSERT INTO classifications (user_id, image_path, prediction, confidence, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (current_user.id, filename, result['prediction'], result['confidence'], datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
            db.commit()

            flash('Klasifikasi berhasil!', 'success')
            return render_template('classification_result.html', result=result, image_path=filename)

    return render_template('classification_new.html')

@app.route('/classification_results')
@login_required
def classification_results():
    page = request.args.get('page', 1, type=int)
    per_page = 6  # Jumlah hasil klasifikasi per halaman

    db = get_db()

    # Hitung total klasifikasi untuk pagination
    total_count = db.execute('''
        SELECT COUNT(*) as count FROM classifications
        WHERE user_id = ?
    ''', (current_user.id,)).fetchone()['count']

    # Hitung total halaman
    total_pages = (total_count + per_page - 1) // per_page

    # Ambil data klasifikasi dengan pagination
    offset = (page - 1) * per_page
    user_classifications = db.execute('''
        SELECT * FROM classifications
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ''', (current_user.id, per_page, offset)).fetchall()

    return render_template('classification_results.html',
                          classifications=user_classifications,
                          page=page,
                          total_pages=total_pages)

@app.route('/about')
def about():
    return render_template('about_new.html')

@app.route('/profile')
@login_required
def profile():
    db = get_db()
    user_classifications = db.execute('''
        SELECT * FROM classifications
        WHERE user_id = ?
        ORDER BY created_at DESC
    ''', (current_user.id,)).fetchall()

    # Dapatkan informasi lengkap tentang pengguna
    user_info = db.execute('SELECT * FROM users WHERE id = ?', (current_user.id,)).fetchone()

    return render_template('profile_new.html',
                          classifications=user_classifications,
                          user_info=user_info)

@app.route('/update_profile', methods=['POST'])
@login_required
def update_profile():
    full_name = request.form.get('full_name')
    email = request.form.get('email')
    address = request.form.get('address')
    username = request.form.get('username')
    password = request.form.get('password')

    # Validasi input
    if not full_name or not email or not address or not username:
        flash('Semua field harus diisi', 'danger')
        return redirect(url_for('profile'))

    db = get_db()

    # Cek apakah username sudah digunakan oleh pengguna lain
    if username != current_user.username:
        existing_user = db.execute('SELECT * FROM users WHERE username = ? AND id != ?',
                                 (username, current_user.id)).fetchone()
        if existing_user:
            flash('Username sudah digunakan oleh pengguna lain', 'danger')
            return redirect(url_for('profile'))

    # Cek apakah email sudah digunakan oleh pengguna lain
    if email != current_user.email:
        existing_user = db.execute('SELECT * FROM users WHERE email = ? AND id != ?',
                                 (email, current_user.id)).fetchone()
        if existing_user:
            flash('Email sudah digunakan oleh pengguna lain', 'danger')
            return redirect(url_for('profile'))

    # Update informasi pengguna
    if password:
        # Hash password baru
        hashed_password = generate_password_hash(password)

        # Update dengan password baru
        db.execute('''
            UPDATE users
            SET username = ?, email = ?, password = ?, full_name = ?, address = ?
            WHERE id = ?
        ''', (username, email, hashed_password, full_name, address, current_user.id))
    else:
        # Update tanpa mengubah password
        db.execute('''
            UPDATE users
            SET username = ?, email = ?, full_name = ?, address = ?
            WHERE id = ?
        ''', (username, email, full_name, address, current_user.id))

    db.commit()

    # Update objek current_user
    current_user.username = username
    current_user.email = email
    current_user.full_name = full_name
    current_user.address = address

    flash('Profil berhasil diperbarui', 'success')
    return redirect(url_for('profile'))

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Anda telah keluar dari sistem', 'info')
    return redirect(url_for('login'))

# Admin routes
@app.route('/admin/users')
@login_required
def admin_users():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    db = get_db()
    users = db.execute('SELECT * FROM users').fetchall()

    return render_template('admin_users_new.html', users=users)

@app.route('/admin/rice_detector')
@login_required
def admin_rice_detector():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('admin_rice_detector.html')

@app.route('/admin/train', methods=['GET', 'POST'])
@login_required
def admin_train():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        action = request.form.get('action', 'train_disease')

        if action == 'train_disease':
            # Panggil fungsi untuk melatih model klasifikasi penyakit
            from models.model import train_model
            result = train_model()

            if result['success']:
                flash(f"Model klasifikasi penyakit berhasil dilatih dengan akurasi {result['accuracy']:.2f}%", 'success')
            else:
                flash(f"Gagal melatih model klasifikasi penyakit: {result['error']}", 'danger')

        elif action == 'train_rice_detector':
            # Panggil fungsi untuk melatih model deteksi tanaman padi
            from models.model import train_rice_detection_model
            result = train_rice_detection_model()

            if result['success']:
                flash(f"Model deteksi tanaman padi berhasil dilatih dengan akurasi {result['accuracy']:.2f}%", 'success')
                flash(f"Dataset: {result['rice_samples']} gambar padi, {result['non_rice_samples']} gambar non-padi", 'info')
            else:
                flash(f"Gagal melatih model deteksi tanaman padi: {result['error']}", 'danger')

    # Load model info untuk ditampilkan di template
    model_info = None
    try:
        model_path = os.path.join('static', 'models', 'random_forest_model.pkl')
        info_json_path = os.path.join('static', 'models', 'model_info.json')

        if os.path.exists(model_path) and os.path.exists(info_json_path):
            with open(info_json_path, 'r', encoding='utf-8') as f:
                model_info = json.load(f)
    except Exception as e:
        print(f"Error loading model info for template: {e}")

    return render_template('admin_train_new.html', model_info=model_info)

@app.route('/admin/evaluate')
@login_required
def admin_evaluate():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    # Cek apakah model sudah dilatih
    model_path = os.path.join(app.config['MODEL_FOLDER'], 'random_forest_model.pkl')
    info_json_path = os.path.join(app.config['MODEL_FOLDER'], 'model_info.json')

    if not os.path.exists(model_path):
        flash('Model belum dilatih. Silakan latih model terlebih dahulu.', 'warning')
        return redirect(url_for('admin_train'))

    # Muat informasi model
    model_info = {}
    if os.path.exists(info_json_path):
        try:
            with open(info_json_path, 'r') as f:
                model_info = json.load(f)
        except Exception as e:
            flash(f'Error saat memuat informasi model: {str(e)}', 'danger')
    else:
        # Jika file JSON tidak ada, coba baca dari file TXT (untuk kompatibilitas)
        info_txt_path = os.path.join(app.config['MODEL_FOLDER'], 'model_info.txt')
        if os.path.exists(info_txt_path):
            try:
                with open(info_txt_path, 'r') as f:
                    for line in f:
                        if ':' in line:
                            key, value = line.strip().split(':', 1)
                            model_info[key.strip()] = value.strip()

                # Konversi nilai ke tipe data yang sesuai
                if 'accuracy' in model_info:
                    model_info['accuracy'] = float(model_info['accuracy'])
                if 'n_samples' in model_info:
                    model_info['n_samples'] = int(model_info['n_samples'])
                if 'n_features' in model_info:
                    model_info['n_features'] = int(model_info['n_features'])
                if 'classes' in model_info and model_info['classes'].startswith('[') and model_info['classes'].endswith(']'):
                    model_info['classes'] = eval(model_info['classes'])

                # Tambahkan data dummy untuk kompatibilitas
                model_info['class_metrics'] = {}
                for class_name in model_info.get('classes', []):
                    model_info['class_metrics'][class_name] = {
                        "precision": 0.0,
                        "recall": 0.0,
                        "f1": 0.0,
                        "support": 0
                    }
                model_info['classification_report'] = "Tidak tersedia"
                model_info['examples'] = []
                model_info['test_samples'] = 0
            except Exception as e:
                flash(f'Error saat memuat informasi model: {str(e)}', 'danger')

    return render_template('admin_evaluate_new.html', model_info=model_info)

# API routes
@app.route('/api/disease_info')
def api_disease_info():
    disease_name = request.args.get('name')
    if not disease_name:
        return jsonify({"success": False, "error": "Nama penyakit tidak diberikan"})

    # Konversi nama penyakit dari format model ke format database
    # Contoh: "Bacterial_Leaf_Blight" atau "Bacterial Leaf Blight" -> "Bacterial Leaf Blight"
    formatted_name = disease_name.replace('_', ' ')

    db = get_db()
    # Coba cari dengan nama yang sudah diformat
    disease = db.execute('SELECT * FROM diseases WHERE name = ?', (formatted_name,)).fetchone()

    # Jika tidak ditemukan, coba cari dengan nama asli
    if not disease:
        disease = db.execute('SELECT * FROM diseases WHERE name = ?', (disease_name,)).fetchone()

    # Jika masih tidak ditemukan, coba cari dengan nama yang diubah ke lowercase
    if not disease:
        disease = db.execute('SELECT * FROM diseases WHERE LOWER(name) = LOWER(?)', (formatted_name,)).fetchone()

    if disease:
        return jsonify({
            "success": True,
            "disease": {
                "id": disease['id'],
                "name": disease['name'],
                "description": disease['description'],
                "symptoms": disease['symptoms'],
                "treatment": disease['treatment']
            }
        })
    else:
        # Log untuk debugging
        print(f"Penyakit tidak ditemukan: {disease_name} (formatted: {formatted_name})")
        return jsonify({"success": False, "error": "Penyakit tidak ditemukan"})

@app.route('/api/model_info_test')
def api_model_info_test():
    """Test endpoint dengan data hardcoded"""
    return jsonify({
        "success": True,
        "info": {
            "timestamp": "2025-06-03 17:49:47",
            "accuracy": 85.64,
            "n_samples": 1010,
            "classes": ["Bacterial Leaf Blight", "Blast", "Brown Spot", "Sehat", "Tungro"]
        }
    })

@app.route('/test_template')
def test_template():
    # Test template dengan data hardcode
    model_info = {
        "timestamp": "2025-06-03 17:49:47",
        "accuracy": 85.64356435643565,
        "n_samples": 1010,
        "classes": ["Bacterial Leaf Blight", "Blast", "Brown Spot", "Sehat", "Tungro"]
    }
    return f"""
    <h1>Test Template Data</h1>
    <p>Model Info: {model_info}</p>
    <p>Status: {'Model tersedia' if model_info else 'Model belum tersedia'}</p>
    <p>Timestamp: {model_info.get('timestamp') if model_info else 'Tidak tersedia'}</p>
    <p>Accuracy: {model_info.get('accuracy'):.2f}% if model_info and model_info.get('accuracy') else 'Tidak tersedia'</p>
    <p>Samples: {model_info.get('n_samples') if model_info else 'Tidak tersedia'}</p>
    <p>Classes: {', '.join(model_info.get('classes', [])) if model_info and model_info.get('classes') else 'Tidak tersedia'}</p>
    """

@app.route('/test_api')
def test_api():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test API Model Info</title>
    </head>
    <body>
        <h1>Test API Model Info</h1>
        <button onclick="testAPI()">Test API</button>
        <div id="result"></div>

        <script>
        function testAPI() {
            console.log('Testing API...');
            document.getElementById('result').innerHTML = 'Loading...';

            fetch('/api/model_info')
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Response text:', text);
                    document.getElementById('result').innerHTML = '<pre>' + text + '</pre>';

                    try {
                        const data = JSON.parse(text);
                        console.log('Parsed JSON:', data);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    document.getElementById('result').innerHTML = 'Error: ' + error.message;
                });
        }
        </script>
    </body>
    </html>
    '''

@app.route('/api/model_info')
def api_model_info():
    try:
        print("=== API model_info dipanggil ===")

        # Path file
        model_path = os.path.join('static', 'models', 'random_forest_model.pkl')
        info_json_path = os.path.join('static', 'models', 'model_info.json')

        print(f"Model path: {model_path}")
        print(f"JSON path: {info_json_path}")
        print(f"Model exists: {os.path.exists(model_path)}")
        print(f"JSON exists: {os.path.exists(info_json_path)}")

        # Cek apakah model ada
        if not os.path.exists(model_path):
            print("Model tidak ditemukan")
            response = jsonify({"success": False, "error": "Model tidak ditemukan"})
            response.headers['Content-Type'] = 'application/json'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            return response

        # Cek apakah file info JSON ada
        if not os.path.exists(info_json_path):
            print("File info JSON tidak ditemukan")
            response = jsonify({"success": False, "error": "Informasi model tidak ditemukan"})
            response.headers['Content-Type'] = 'application/json'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            return response

        # Baca file JSON
        print("Membaca file JSON...")
        with open(info_json_path, 'r', encoding='utf-8') as f:
            info = json.load(f)

        print(f"JSON berhasil dibaca dengan {len(info)} keys")
        print(f"Timestamp: {info.get('timestamp')}")
        print(f"Accuracy: {info.get('accuracy')}")

        # Return response dengan header yang benar
        response = jsonify({"success": True, "info": info})
        response.headers['Content-Type'] = 'application/json'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        print("Response berhasil dibuat")
        return response

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        response = jsonify({"success": False, "error": str(e)})
        response.headers['Content-Type'] = 'application/json'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response

@app.route('/admin/upload_dataset', methods=['POST'])
@login_required
def admin_upload_dataset():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    if 'dataset' not in request.files:
        flash('Tidak ada file yang dipilih', 'danger')
        return redirect(url_for('admin_train'))

    file = request.files['dataset']

    if file.filename == '':
        flash('Tidak ada file yang dipilih', 'danger')
        return redirect(url_for('admin_train'))

    if file and file.filename.endswith('.zip'):
        import zipfile
        import shutil

        # Simpan file zip
        zip_path = os.path.join(app.config['UPLOAD_FOLDER'], 'dataset.zip')
        file.save(zip_path)

        # Buat direktori dataset jika belum ada
        dataset_dir = 'dataset'
        if os.path.exists(dataset_dir):
            shutil.rmtree(dataset_dir)
        os.makedirs(dataset_dir)

        # Ekstrak zip ke direktori dataset
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(dataset_dir)

        flash('Dataset berhasil diunggah dan diekstrak', 'success')
        return redirect(url_for('admin_train'))
    else:
        flash('Format file tidak didukung. Harap unggah file ZIP.', 'danger')
        return redirect(url_for('admin_train'))

@app.route('/admin/upload_non_rice_dataset', methods=['POST'])
@login_required
def admin_upload_non_rice_dataset():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    if 'non_rice_dataset' not in request.files:
        flash('Tidak ada file yang dipilih', 'danger')
        return redirect(url_for('admin_train'))

    file = request.files['non_rice_dataset']

    if file.filename == '':
        flash('Tidak ada file yang dipilih', 'danger')
        return redirect(url_for('admin_train'))

    if file and file.filename.endswith('.zip'):
        import zipfile
        import shutil

        # Simpan file zip
        zip_path = os.path.join(app.config['UPLOAD_FOLDER'], 'non_rice_dataset.zip')
        file.save(zip_path)

        # Buat direktori dataset non-padi jika belum ada
        non_rice_dataset_dir = 'dataset_non_rice'
        if os.path.exists(non_rice_dataset_dir):
            shutil.rmtree(non_rice_dataset_dir)
        os.makedirs(non_rice_dataset_dir)

        # Ekstrak zip ke direktori dataset non-padi
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(non_rice_dataset_dir)

        flash('Dataset non-padi berhasil diunggah dan diekstrak', 'success')
        return redirect(url_for('admin_train'))
    else:
        flash('Format file tidak didukung. Harap unggah file ZIP.', 'danger')
        return redirect(url_for('admin_train'))

@app.route('/admin/add_user', methods=['POST'])
@login_required
def admin_add_user():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    full_name = request.form.get('full_name')
    email = request.form.get('email')
    address = request.form.get('address')
    username = request.form.get('username')
    password = request.form.get('password')
    role = request.form.get('role')

    if not full_name or not email or not username or not password or not role:
        flash('Semua field harus diisi', 'danger')
        return redirect(url_for('admin_users'))

    # Hash password
    hashed_password = generate_password_hash(password)

    # Simpan user baru ke database
    db = get_db()
    try:
        db.execute('''
            INSERT INTO users (username, email, password, full_name, address, role)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, email, hashed_password, full_name, address, role))
        db.commit()
        flash('Pengguna baru berhasil ditambahkan', 'success')
    except sqlite3.IntegrityError:
        flash('Username atau email sudah digunakan', 'danger')

    return redirect(url_for('admin_users'))

@app.route('/admin/edit_user/<int:user_id>', methods=['POST'])
@login_required
def admin_edit_user(user_id):
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    full_name = request.form.get('full_name')
    email = request.form.get('email')
    address = request.form.get('address')
    username = request.form.get('username')
    password = request.form.get('password')
    role = request.form.get('role')

    if not full_name or not email or not username or not role:
        flash('Nama lengkap, email, username, dan role harus diisi', 'danger')
        return redirect(url_for('admin_users'))

    db = get_db()

    if password:
        # Update dengan password baru
        hashed_password = generate_password_hash(password)
        db.execute('''
            UPDATE users
            SET username = ?, email = ?, password = ?, full_name = ?, address = ?, role = ?
            WHERE id = ?
        ''', (username, email, hashed_password, full_name, address, role, user_id))
    else:
        # Update tanpa mengubah password
        db.execute('''
            UPDATE users
            SET username = ?, email = ?, full_name = ?, address = ?, role = ?
            WHERE id = ?
        ''', (username, email, full_name, address, role, user_id))

    db.commit()
    flash('Pengguna berhasil diperbarui', 'success')
    return redirect(url_for('admin_users'))

@app.route('/admin/delete_user/<int:user_id>')
@login_required
def admin_delete_user(user_id):
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    # Pastikan tidak menghapus diri sendiri
    if int(user_id) == int(current_user.id):
        flash('Anda tidak dapat menghapus akun Anda sendiri', 'danger')
        return redirect(url_for('admin_users'))

    db = get_db()

    # Hapus klasifikasi yang terkait dengan pengguna
    db.execute('DELETE FROM classifications WHERE user_id = ?', (user_id,))

    # Hapus pengguna
    db.execute('DELETE FROM users WHERE id = ?', (user_id,))
    db.commit()

    flash('Pengguna berhasil dihapus', 'success')
    return redirect(url_for('admin_users'))

# Route untuk halaman pengelolaan penyakit
@app.route('/admin/diseases')
@login_required
def admin_diseases():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    db = get_db()
    diseases = db.execute('SELECT * FROM diseases ORDER BY name').fetchall()
    return render_template('admin_diseases_new.html', diseases=diseases)

# Route untuk menambah penyakit baru
@app.route('/admin/add_disease', methods=['POST'])
@login_required
def admin_add_disease():
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    name = request.form.get('name')
    description = request.form.get('description')
    symptoms = request.form.get('symptoms')
    treatment = request.form.get('treatment')

    if not name or not description or not symptoms or not treatment:
        flash('Semua field harus diisi', 'danger')
        return redirect(url_for('admin_diseases'))

    db = get_db()
    try:
        db.execute('''
            INSERT INTO diseases (name, description, symptoms, treatment)
            VALUES (?, ?, ?, ?)
        ''', (name, description, symptoms, treatment))
        db.commit()
        flash('Penyakit baru berhasil ditambahkan', 'success')
    except sqlite3.IntegrityError:
        flash('Nama penyakit sudah ada', 'danger')

    return redirect(url_for('admin_diseases'))

# Route untuk mengedit penyakit
@app.route('/admin/edit_disease/<int:disease_id>', methods=['POST'])
@login_required
def admin_edit_disease(disease_id):
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    name = request.form.get('name')
    description = request.form.get('description')
    symptoms = request.form.get('symptoms')
    treatment = request.form.get('treatment')

    if not name or not description or not symptoms or not treatment:
        flash('Semua field harus diisi', 'danger')
        return redirect(url_for('admin_diseases'))

    db = get_db()
    db.execute('''
        UPDATE diseases
        SET name = ?, description = ?, symptoms = ?, treatment = ?
        WHERE id = ?
    ''', (name, description, symptoms, treatment, disease_id))
    db.commit()

    flash('Penyakit berhasil diperbarui', 'success')
    return redirect(url_for('admin_diseases'))

# Route untuk menghapus penyakit
@app.route('/admin/delete_disease/<int:disease_id>')
@login_required
def admin_delete_disease(disease_id):
    if current_user.role != 'admin':
        flash('Anda tidak memiliki akses ke halaman ini', 'danger')
        return redirect(url_for('dashboard'))

    db = get_db()

    # Periksa apakah penyakit digunakan dalam klasifikasi
    classifications = db.execute('SELECT COUNT(*) as count FROM classifications WHERE prediction = (SELECT name FROM diseases WHERE id = ?)', (disease_id,)).fetchone()

    if classifications and classifications['count'] > 0:
        flash('Penyakit ini tidak dapat dihapus karena digunakan dalam data klasifikasi', 'danger')
        return redirect(url_for('admin_diseases'))

    # Hapus penyakit
    db.execute('DELETE FROM diseases WHERE id = ?', (disease_id,))
    db.commit()

    flash('Penyakit berhasil dihapus', 'success')
    return redirect(url_for('admin_diseases'))

if __name__ == '__main__':
    # Cek apakah database sudah ada, jika belum, inisialisasi
    if not os.path.exists(app.config['DATABASE']):
        init_db()
    else:
        # Jika database sudah ada, jalankan migrasi
        migrate_db()
    app.run(debug=True)
