{% extends "base.html" %}

{% block title %}Profil - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_style %}
.profile-header {
    text-align: center;
    margin-bottom: 30px;
}
.profile-header img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin-bottom: 15px;
}
.profile-header h3 {
    margin-bottom: 5px;
}
.profile-header .badge {
    font-size: 14px;
    padding: 5px 10px;
}
.admin-badge {
    background-color: #dc3545;
}
.user-badge {
    background-color: #17a2b8;
}
{% endblock %}

{% block content %}
<div class="profile-header">
    <img src="https://ui-avatars.com/api/?name={{ current_user.full_name|default(current_user.username) }}&background=random&size=120" alt="User">
    <h3>{{ current_user.full_name|default(current_user.username) }}</h3>
    <p class="text-muted">@{{ current_user.username }}</p>
    <span class="badge {% if current_user.role == 'admin' %}admin-badge{% else %}user-badge{% endif %}">
        {{ current_user.role }}
    </span>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-edit me-2"></i> Informasi Pengguna
            </div>
            <div class="card-body">
                <form action="{{ url_for('update_profile') }}" method="post">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="{{ current_user.full_name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Alamat/Wilayah Pertanian</label>
                        <input type="text" class="form-control" id="address" name="address" value="{{ current_user.address }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password Baru (Kosongkan jika tidak ingin mengubah)</label>
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Simpan Perubahan
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i> Informasi Akun
            </div>
            <div class="card-body">
                <p><strong>ID Pengguna:</strong> {{ current_user.id }}</p>
                <p><strong>Email:</strong> {{ current_user.email }}</p>
                <p><strong>Alamat/Wilayah Pertanian:</strong> {{ current_user.address|default('Belum diisi') }}</p>
                <p><strong>Tanggal Bergabung:</strong> {{ user_info.created_at }}</p>
                <p><strong>Total Klasifikasi:</strong> {{ classifications|length }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-history me-2"></i> Riwayat Klasifikasi
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Gambar</th>
                        <th>Prediksi</th>
                        <th>Kepercayaan</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    {% for classification in classifications %}
                    <tr>
                        <td>{{ classification.id }}</td>
                        <td>
                            <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                 alt="Gambar Tanaman" class="img-thumbnail" style="width: 50px;">
                        </td>
                        <td>{{ classification.prediction }}</td>
                        <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                        <td>{{ classification.created_at }}</td>
                        <td>
                            <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#detailModal{{ classification.id }}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>

                    <!-- Detail Modal -->
                    <div class="modal fade" id="detailModal{{ classification.id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ classification.id }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="detailModalLabel{{ classification.id }}">Detail Klasifikasi #{{ classification.id }}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                                 alt="Gambar Tanaman" class="img-fluid rounded">
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Hasil Klasifikasi</h5>
                                            <p><strong>Prediksi:</strong> <span class="prediction">{{ classification.prediction }}</span></p>
                                            <p><strong>Kepercayaan:</strong> {{ "%.2f"|format(classification.confidence) }}%</p>
                                            <p><strong>Tanggal:</strong> {{ classification.created_at }}</p>

                                            <h5 class="mt-4">Informasi Penyakit</h5>
                                            <div id="disease-info-{{ classification.id }}">
                                                <div class="text-center">
                                                    <div class="spinner-border text-success" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p>Memuat informasi penyakit...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">Belum ada riwayat klasifikasi</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
// Fungsi untuk memuat informasi penyakit
function loadDiseaseInfo(diseaseName, elementId) {
    fetch(`/api/disease_info?name=${diseaseName}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                var diseaseInfo = document.getElementById(elementId);
                diseaseInfo.innerHTML = `
                    <p><strong>Deskripsi:</strong> ${data.disease.description}</p>
                    <p><strong>Gejala:</strong> ${data.disease.symptoms}</p>
                    <p><strong>Penanganan:</strong> ${data.disease.treatment}</p>
                `;
            } else {
                var diseaseInfo = document.getElementById(elementId);
                diseaseInfo.innerHTML = `<p class="text-danger">Informasi penyakit tidak ditemukan.</p>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            var diseaseInfo = document.getElementById(elementId);
            diseaseInfo.innerHTML = `<p class="text-danger">Terjadi kesalahan saat memuat informasi penyakit.</p>`;
        });
}

// Memuat informasi penyakit saat modal dibuka
{% for classification in classifications %}
document.getElementById('detailModal{{ classification.id }}').addEventListener('shown.bs.modal', function () {
    loadDiseaseInfo('{{ classification.prediction }}', 'disease-info-{{ classification.id }}');
});
{% endfor %}
{% endblock %}
