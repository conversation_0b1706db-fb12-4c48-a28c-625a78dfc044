<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Penyakit - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .disease-badge {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            <a href="{{ url_for('admin_evaluate') }}">
                <i class="fas fa-chart-bar"></i> Evaluasi Model
            </a>
            <a href="{{ url_for('admin_diseases') }}" class="active">
                <i class="fas fa-virus"></i> Kelola Penyakit
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>
        
        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Kelola Penyakit</h2>
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDiseaseModal">
                    <i class="fas fa-plus me-2"></i> Tambah Penyakit
                </button>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-virus me-2"></i> Daftar Penyakit
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama</th>
                                    <th>Deskripsi</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for disease in diseases %}
                                <tr>
                                    <td>{{ disease.id }}</td>
                                    <td>
                                        <span class="badge disease-badge">{{ disease.name }}</span>
                                    </td>
                                    <td>{{ disease.description|truncate(100) }}</td>
                                    <td>{{ disease.created_at }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDiseaseModal{{ disease.id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDiseaseModal{{ disease.id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDiseaseModal{{ disease.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- View Disease Modal -->
                                <div class="modal fade" id="viewDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="viewDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="viewDiseaseModalLabel{{ disease.id }}">Detail Penyakit: {{ disease.name }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <h5>Deskripsi</h5>
                                                <p>{{ disease.description }}</p>
                                                
                                                <h5>Gejala</h5>
                                                <p>{{ disease.symptoms }}</p>
                                                
                                                <h5>Penanganan</h5>
                                                <p>{{ disease.treatment }}</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Edit Disease Modal -->
                                <div class="modal fade" id="editDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="editDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="editDiseaseModalLabel{{ disease.id }}">Edit Penyakit</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form action="{{ url_for('admin_edit_disease', disease_id=disease.id) }}" method="post">
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <label for="name{{ disease.id }}" class="form-label">Nama Penyakit</label>
                                                        <input type="text" class="form-control" id="name{{ disease.id }}" name="name" value="{{ disease.name }}" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="description{{ disease.id }}" class="form-label">Deskripsi</label>
                                                        <textarea class="form-control" id="description{{ disease.id }}" name="description" rows="4" required>{{ disease.description }}</textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="symptoms{{ disease.id }}" class="form-label">Gejala</label>
                                                        <textarea class="form-control" id="symptoms{{ disease.id }}" name="symptoms" rows="4" required>{{ disease.symptoms }}</textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="treatment{{ disease.id }}" class="form-label">Penanganan</label>
                                                        <textarea class="form-control" id="treatment{{ disease.id }}" name="treatment" rows="4" required>{{ disease.treatment }}</textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Delete Disease Modal -->
                                <div class="modal fade" id="deleteDiseaseModal{{ disease.id }}" tabindex="-1" aria-labelledby="deleteDiseaseModalLabel{{ disease.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteDiseaseModalLabel{{ disease.id }}">Hapus Penyakit</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Apakah Anda yakin ingin menghapus penyakit <strong>{{ disease.name }}</strong>?</p>
                                                <p class="text-danger">Tindakan ini tidak dapat dibatalkan dan mungkin mempengaruhi hasil klasifikasi.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                <a href="{{ url_for('admin_delete_disease', disease_id=disease.id) }}" class="btn btn-danger">Hapus Penyakit</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Disease Modal -->
    <div class="modal fade" id="addDiseaseModal" tabindex="-1" aria-labelledby="addDiseaseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDiseaseModalLabel">Tambah Penyakit Baru</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('admin_add_disease') }}" method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nama Penyakit</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="symptoms" class="form-label">Gejala</label>
                            <textarea class="form-control" id="symptoms" name="symptoms" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="treatment" class="form-label">Penanganan</label>
                            <textarea class="form-control" id="treatment" name="treatment" rows="4" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-success">Tambah Penyakit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
