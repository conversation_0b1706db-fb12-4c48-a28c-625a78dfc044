import json
import os

# Test template rendering
print("=== Test Template Data ===")

# Load model info seperti di app.py
model_info = None
try:
    model_path = os.path.join('static', 'models', 'random_forest_model.pkl')
    info_json_path = os.path.join('static', 'models', 'model_info.json')
    
    print(f"Model path: {model_path}")
    print(f"JSON path: {info_json_path}")
    print(f"Model exists: {os.path.exists(model_path)}")
    print(f"JSON exists: {os.path.exists(info_json_path)}")
    
    if os.path.exists(model_path) and os.path.exists(info_json_path):
        with open(info_json_path, 'r', encoding='utf-8') as f:
            model_info = json.load(f)
        print("Model info loaded successfully!")
        print(f"Timestamp: {model_info.get('timestamp')}")
        print(f"Accuracy: {model_info.get('accuracy')}")
        print(f"Samples: {model_info.get('n_samples')}")
        print(f"Classes: {model_info.get('classes')}")
    else:
        print("Model or info file not found")
        
except Exception as e:
    print(f"Error: {e}")

print(f"\nmodel_info is None: {model_info is None}")
if model_info:
    print("Template should show model data")
else:
    print("Template should show 'Tidak tersedia'")
