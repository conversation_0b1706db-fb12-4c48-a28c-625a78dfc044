/* Background styles for login page */
body {
    background-image: url('../images/rice-field-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
}

body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Overlay hitam transparan */
    z-index: -1;
}

.login-container {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    padding: 40px;
    max-width: 500px;
    margin: 80px auto;
    position: relative;
    z-index: 1;
}

.login-header h2 {
    color: #28a745;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.login-header p {
    color: #495057;
    font-size: 16px;
}

.btn-login {
    background-color: #28a745;
    border: none;
    padding: 12px;
    font-weight: 600;
    width: 100%;
    border-radius: 5px;
    transition: all 0.3s;
}

.btn-login:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.login-footer {
    margin-top: 25px;
    text-align: center;
    color: #495057;
}

.login-footer a {
    color: #28a745;
    font-weight: 600;
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .login-container {
        margin: 40px auto;
        padding: 30px;
    }
}

@media (max-width: 576px) {
    .login-container {
        margin: 20px;
        padding: 20px;
    }
}
