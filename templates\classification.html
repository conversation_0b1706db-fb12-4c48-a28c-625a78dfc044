<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Klasifikasi Penyakit - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .upload-area {
            border: 2px dashed #28a745;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            background-color: #e9ecef;
        }
        .upload-icon {
            font-size: 50px;
            color: #28a745;
            margin-bottom: 15px;
        }
        #preview-image {
            max-width: 100%;
            max-height: 300px;
            margin-top: 20px;
            border-radius: 5px;
            display: none;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}" class="active">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>
        
        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <h2 class="mb-4">Klasifikasi Penyakit Tanaman Padi</h2>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-upload me-2"></i> Unggah Gambar Tanaman Padi
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div class="upload-area" id="upload-area" onclick="document.getElementById('image').click();">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h5>Klik atau seret gambar ke sini</h5>
                            <p class="text-muted">Format yang didukung: JPG, JPEG, PNG (Maks. 16MB)</p>
                            <input type="file" id="image" name="image" accept="image/*" style="display: none;" onchange="previewImage(this);">
                            <img id="preview-image" src="#" alt="Preview">
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg" id="classify-btn" disabled>
                                <i class="fas fa-microscope me-2"></i> Klasifikasi Penyakit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> Petunjuk Penggunaan
                </div>
                <div class="card-body">
                    <ol>
                        <li>Unggah gambar daun tanaman padi yang ingin diklasifikasi.</li>
                        <li>Pastikan gambar memiliki pencahayaan yang baik dan fokus pada bagian yang terinfeksi.</li>
                        <li>Klik tombol "Klasifikasi Penyakit" untuk memulai proses klasifikasi.</li>
                        <li>Sistem akan menampilkan hasil klasifikasi beserta tingkat kepercayaan.</li>
                        <li>Hasil klasifikasi akan disimpan dalam riwayat dan dapat dilihat di halaman profil.</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function previewImage(input) {
            var preview = document.getElementById('preview-image');
            var classifyBtn = document.getElementById('classify-btn');
            
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    classifyBtn.disabled = false;
                }
                
                reader.readAsDataURL(input.files[0]);
            } else {
                preview.style.display = 'none';
                classifyBtn.disabled = true;
            }
        }
        
        // Drag and drop functionality
        var uploadArea = document.getElementById('upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#e9ecef';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f8f9fa';
            
            var file = e.dataTransfer.files[0];
            var input = document.getElementById('image');
            
            // Create a new FileList object
            var dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            input.files = dataTransfer.files;
            
            previewImage(input);
        });
    </script>
</body>
</html>
