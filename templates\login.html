<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
        background: url("{{ url_for('static', filename='images/ricepaddy.jpg') }}") no-repeat center center fixed;
        background-size: cover;
    }
        .role-selector {
            display: flex;
            margin-bottom: 20px;
            border-radius: 5px;
            overflow: hidden;
        }
        .role-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            background-color: #f8f9fa;
            border: none;
            transition: all 0.3s;
            font-weight: 600;
        }
        .role-btn.active {
            background-color: #28a745;
            color: white;
        }
        .role-btn:first-child {
            border-right: 1px solid #dee2e6;
        }
        .quick-login {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .quick-login-btn {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
            cursor: pointer;
        }
        .quick-login-btn:hover {
            background-color: #e9ecef;
        }
        .quick-login-btn.admin {
            border-color: #dc3545;
            color: #dc3545;
        }
        .quick-login-btn.admin:hover {
            background-color: #dc3545;
            color: white;
        }
        .quick-login-btn.user {
            border-color: #17a2b8;
            color: #17a2b8;
        }
        .quick-login-btn.user:hover {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <img src="{{ url_for('static', filename='images/leaf-nature-forest.png') }}" alt="Logo" style="width: 80px; height: 80px;">
                <h2>PadiKu</h2>
                <p class="text-muted">Sistem Klasifikasi Penyakit Tanaman Padi</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="role-selector">
                <button type="button" class="role-btn active" id="user-role-btn">
                    <i class="fas fa-user me-2"></i> User
                </button>
                <button type="button" class="role-btn" id="admin-role-btn">
                    <i class="fas fa-user-shield me-2"></i> Admin
                </button>
            </div>

            <form method="post" action="{{ url_for('login') }}">
                <input type="hidden" id="role" name="role" value="user">
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    </div>
                </div>
                <button type="submit" class="btn btn-login btn-success">
                    <i class="fas fa-sign-in-alt me-2"></i> Masuk
                </button>

            </form>

            <div class="login-footer">
                <p>Belum punya akun? <a href="{{ url_for('register') }}">Daftar di sini</a></p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userRoleBtn = document.getElementById('user-role-btn');
            const adminRoleBtn = document.getElementById('admin-role-btn');
            const roleInput = document.getElementById('role');

            // Toggle role buttons
            userRoleBtn.addEventListener('click', function() {
                userRoleBtn.classList.add('active');
                adminRoleBtn.classList.remove('active');
                roleInput.value = 'user';
            });

            adminRoleBtn.addEventListener('click', function() {
                adminRoleBtn.classList.add('active');
                userRoleBtn.classList.remove('active');
                roleInput.value = 'admin';
            });
        });
    </script>
</body>
</html>
