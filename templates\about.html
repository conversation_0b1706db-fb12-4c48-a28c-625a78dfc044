<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tentang - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .about-icon {
            font-size: 40px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .team-member {
            text-align: center;
            margin-bottom: 20px;
        }
        .team-member img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin-bottom: 15px;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}" class="active">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>
        
        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">Tentang Sistem</h2>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> Tentang PadiKu
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-seedling about-icon"></i>
                        <h3>Sistem Klasifikasi Penyakit Tanaman Padi</h3>
                        <p class="text-muted">Menggunakan Algoritma Random Forest</p>
                    </div>
                    
                    <p>PadiKu adalah sistem klasifikasi penyakit tanaman padi berbasis web yang menggunakan algoritma Random Forest untuk mengidentifikasi berbagai penyakit pada tanaman padi melalui gambar. Sistem ini dikembangkan sebagai bagian dari skripsi untuk membantu petani dan peneliti dalam mendiagnosis penyakit tanaman padi dengan cepat dan akurat.</p>
                    
                    <p>Sistem ini mampu mengklasifikasikan beberapa jenis penyakit umum pada tanaman padi, termasuk:</p>
                    <ul>
                        <li>Blast (Pyricularia oryzae)</li>
                        <li>Brown Spot (Cochliobolus miyabeanus)</li>
                        <li>Bacterial Leaf Blight (Xanthomonas oryzae)</li>
                        <li>Tungro (Rice Tungro Virus)</li>
                        <li>Dan tanaman padi sehat</li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-cogs me-2"></i> Teknologi yang Digunakan
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <i class="fab fa-python about-icon"></i>
                            <h4>Python</h4>
                            <p>Bahasa pemrograman utama untuk pengembangan sistem dan model machine learning.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-flask about-icon"></i>
                            <h4>Flask</h4>
                            <p>Framework web Python yang digunakan untuk mengembangkan aplikasi web.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-brain about-icon"></i>
                            <h4>Scikit-learn</h4>
                            <p>Library machine learning yang digunakan untuk implementasi algoritma Random Forest.</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-database about-icon"></i>
                            <h4>SQLite</h4>
                            <p>Database yang digunakan untuk menyimpan data pengguna dan hasil klasifikasi.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fab fa-bootstrap about-icon"></i>
                            <h4>Bootstrap</h4>
                            <p>Framework CSS untuk desain antarmuka pengguna yang responsif.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-image about-icon"></i>
                            <h4>OpenCV</h4>
                            <p>Library pengolahan gambar untuk preprocessing gambar tanaman padi.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-user-graduate me-2"></i> Tim Pengembang
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="team-member">
                                <img src="https://ui-avatars.com/api/?name=Mahasiswa&background=random" alt="Mahasiswa">
                                <h4>Nama Mahasiswa</h4>
                                <p class="text-muted">Pengembang Utama</p>
                                <p>Mahasiswa Teknik Informatika</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="team-member">
                                <img src="https://ui-avatars.com/api/?name=Dosen+Pembimbing&background=random" alt="Dosen Pembimbing">
                                <h4>Nama Dosen Pembimbing</h4>
                                <p class="text-muted">Dosen Pembimbing</p>
                                <p>Fakultas Teknik Informatika</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="team-member">
                                <img src="https://ui-avatars.com/api/?name=Pakar+Pertanian&background=random" alt="Pakar Pertanian">
                                <h4>Nama Pakar Pertanian</h4>
                                <p class="text-muted">Konsultan Pertanian</p>
                                <p>Fakultas Pertanian</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-book me-2"></i> Referensi
                </div>
                <div class="card-body">
                    <ul>
                        <li>Breiman, L. (2001). Random forests. Machine learning, 45(1), 5-32.</li>
                        <li>Mohanty, S. P., Hughes, D. P., & Salathé, M. (2016). Using deep learning for image-based plant disease detection. Frontiers in plant science, 7, 1419.</li>
                        <li>Ferentinos, K. P. (2018). Deep learning models for plant disease detection and diagnosis. Computers and Electronics in Agriculture, 145, 311-318.</li>
                        <li>Lu, Y., Yi, S., Zeng, N., Liu, Y., & Zhang, Y. (2017). Identification of rice diseases using deep convolutional neural networks. Neurocomputing, 267, 378-384.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
