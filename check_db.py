import sqlite3
import os

# Pastikan database ada
db_path = 'database/padiku.db'
if not os.path.exists(db_path):
    print(f"Database tidak ditemukan di {db_path}")
    exit(1)

# Buat koneksi ke database
conn = sqlite3.connect(db_path)
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

try:
    # Tampilkan semua pengguna
    print("Daftar pengguna:")
    cursor.execute('SELECT id, username, email, role, password FROM users')
    for user in cursor.fetchall():
        print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}, Role: {user['role']}")
        print(f"Password hash: {user['password'][:30]}...")
    
    # Tam<PERSON><PERSON>an semua penyakit
    print("\nDaftar penyakit:")
    cursor.execute('SELECT id, name FROM diseases')
    for disease in cursor.fetchall():
        print(f"ID: {disease['id']}, Nama: {disease['name']}")
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    conn.close()
