<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sistem Klasifikasi Penyakit Tanaman Padi</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #28a745;
            padding-top: 20px;
            color: white;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-header img {
            width: 60px;
            margin-bottom: 10px;
        }
        .sidebar-menu {
            padding: 20px 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-card i {
            font-size: 40px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .stat-card .stat-number {
            font-size: 30px;
            font-weight: 700;
            color: #343a40;
        }
        .stat-card .stat-label {
            font-size: 16px;
            color: #6c757d;
        }
        .user-info {
            text-align: center;
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'">
            <h4>PadiKu</h4>
            <p class="mb-0">Sistem Klasifikasi Penyakit</p>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}" class="active">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{{ url_for('classification') }}">
                <i class="fas fa-microscope"></i> Klasifikasi Penyakit
            </a>
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i> Profil
            </a>
            <a href="{{ url_for('about') }}">
                <i class="fas fa-info-circle"></i> Tentang
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin_users') }}">
                <i class="fas fa-users-cog"></i> Kelola Pengguna
            </a>
            <a href="{{ url_for('admin_train') }}">
                <i class="fas fa-brain"></i> Latih Model
            </a>
            <a href="{{ url_for('admin_evaluate') }}">
                <i class="fas fa-chart-bar"></i> Evaluasi Model
            </a>
            <a href="{{ url_for('admin_diseases') }}">
                <i class="fas fa-virus"></i> Kelola Penyakit
            </a>
            {% endif %}
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> Keluar
            </a>
        </div>

        <div class="user-info mt-auto">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=random" alt="User">
            <h6 class="mb-0">{{ current_user.username }}</h6>
            <small>{{ current_user.role }}</small>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <h2 class="mb-4">Dashboard</h2>

            <div class="row">
                <div class="col-md-6">
                    <div class="card stat-card">
                        <i class="fas fa-users"></i>
                        <div class="stat-number">{{ total_users }}</div>
                        <div class="stat-label">Total Pengguna</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card stat-card">
                        <i class="fas fa-microscope"></i>
                        <div class="stat-number">{{ total_classifications }}</div>
                        <div class="stat-label">Total Klasifikasi</div>
                    </div>
                </div>
            </div>

            {% if current_user.role == 'admin' %}
            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-history me-2"></i> Klasifikasi Terbaru
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Pengguna</th>
                                    <th>Gambar</th>
                                    <th>Prediksi</th>
                                    <th>Kepercayaan</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for classification in recent_classifications %}
                                <tr>
                                    <td>{{ classification.id }}</td>
                                    <td>{{ classification.username }}</td>
                                    <td>
                                        <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                             alt="Gambar Tanaman" class="img-thumbnail" style="width: 50px;">
                                    </td>
                                    <td>{{ classification.prediction }}</td>
                                    <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                                    <td>{{ classification.created_at }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">Belum ada data klasifikasi</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> Informasi
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-lightbulb me-2"></i> Untuk melihat riwayat klasifikasi Anda, silakan kunjungi halaman <a href="{{ url_for('profile') }}">Profil</a>.
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
