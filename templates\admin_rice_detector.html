{% extends "base.html" %}

{% block title %}Model <PERSON><PERSON><PERSON>di - Admin{% endblock %}

{% block content %}
<h2 class="mb-4">Model <PERSON><PERSON><PERSON></h2>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-upload me-2"></i> Unggah Dataset Non-Padi
            </div>
            <div class="card-body">
                <form action="{{ url_for('admin_upload_non_rice_dataset') }}" method="post" enctype="multipart/form-data">
                    <div class="upload-area" id="upload-area-non-rice" onclick="document.getElementById('non_rice_dataset').click();">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h5>Klik atau seret file dataset non-padi ke sini</h5>
                        <p class="text-muted">Format yang didukung: ZIP (Maks. 100MB)</p>
                        <input type="file" id="non_rice_dataset" name="non_rice_dataset" accept=".zip" style="display: none;" onchange="updateFileNameNonRice(this);">
                        <p id="selected-file-non-rice" class="mt-3"></p>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg" id="upload-btn-non-rice" disabled>
                            <i class="fas fa-upload me-2"></i> Unggah Dataset Non-Padi
                        </button>
                    </div>
                </form>

                <div class="mt-4">
                    <h5>Dataset Non-Padi:</h5>
                    <p class="text-muted">Dataset ini berisi gambar-gambar yang bukan tanaman padi, seperti:</p>
                    <ul class="text-muted">
                        <li>Tanaman lain (jagung, kedelai, dll)</li>
                        <li>Objek non-tanaman (bangunan, kendaraan, dll)</li>
                        <li>Pemandangan umum</li>
                        <li>Hewan</li>
                    </ul>
                    <pre class="bg-light p-3 rounded">
non_rice_dataset.zip
├── corn/
│   ├── image1.jpg
│   └── ...
├── buildings/
│   ├── image1.jpg
│   └── ...
├── animals/
│   ├── image1.jpg
│   └── ...
└── others/
    ├── image1.jpg
    └── ...
                    </pre>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-brain me-2"></i> Latih Model Deteksi Padi
            </div>
            <div class="card-body text-center">
                <i class="fas fa-seedling train-icon"></i>
                <h4 class="mb-3">Latih Model Deteksi Tanaman Padi</h4>
                <p>Model ini akan membedakan antara gambar tanaman padi dan gambar lainnya. Pastikan dataset penyakit padi dan dataset non-padi sudah diunggah.</p>

                <form action="{{ url_for('admin_train') }}" method="post">
                    <input type="hidden" name="action" value="train_rice_detector">
                    <button type="submit" class="btn btn-warning btn-lg">
                        <i class="fas fa-play me-2"></i> Latih Model Deteksi
                    </button>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i> Informasi Model Deteksi
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Catatan:</strong> Model deteksi tanaman padi akan digunakan untuk memfilter gambar sebelum klasifikasi penyakit. Hanya gambar yang terdeteksi sebagai tanaman padi yang akan diklasifikasi penyakitnya.
                </div>
                
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Status Saat Ini:</strong> Sistem menggunakan deteksi sederhana berdasarkan warna hijau (akurasi ~70-80%). Setelah melatih model ML, akurasi akan meningkat menjadi ~90-95%.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-arrow-left me-2"></i> Navigasi
    </div>
    <div class="card-body">
        <a href="{{ url_for('admin_train') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali ke Latih Model
        </a>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateFileNameNonRice(input) {
    var fileName = input.files[0] ? input.files[0].name : '';
    var fileInfo = document.getElementById('selected-file-non-rice');
    var uploadBtn = document.getElementById('upload-btn-non-rice');

    if (fileName) {
        fileInfo.textContent = 'File terpilih: ' + fileName;
        uploadBtn.disabled = false;
    } else {
        fileInfo.textContent = '';
        uploadBtn.disabled = true;
    }
}

// Drag and drop functionality for non-rice dataset
var uploadAreaNonRice = document.getElementById('upload-area-non-rice');

if (uploadAreaNonRice) {
    uploadAreaNonRice.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#e9ecef';
    });

    uploadAreaNonRice.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#f8f9fa';
    });

    uploadAreaNonRice.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadAreaNonRice.style.backgroundColor = '#f8f9fa';

        var file = e.dataTransfer.files[0];
        var input = document.getElementById('non_rice_dataset');

        // Create a new FileList object
        var dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        input.files = dataTransfer.files;

        updateFileNameNonRice(input);
    });
}
</script>

<style>
.upload-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}

.upload-area:hover {
    border-color: #28a745;
    background-color: #e9ecef;
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 20px;
}

.train-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 20px;
}
</style>
{% endblock %}
